<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AdminFunctions;
use App\Http\Requests\StoreAdminFunctionsRequest;
use App\Http\Requests\UpdateAdminFunctionsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\AdminFunctionsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class AdminFunctionsController extends Controller
{
    use ApiErrorResponse;

    private $AdminFunctionsRepositoryInterface;

    
            public function __construct(AdminFunctionsRepositoryInterface $AdminFunctionsRepositoryInterface)
            {
                $this->AdminFunctionsRepositoryInterface = $AdminFunctionsRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->AdminFunctionsRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreAdminFunctionsRequest $request)
            {
                $state = $this->AdminFunctionsRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($AdminFunctions)
            {
                $state = $this->AdminFunctionsRepositoryInterface->show($AdminFunctions);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateAdminFunctionsRequest $request, AdminFunctions $AdminFunctions)
            {
                $state = $this->AdminFunctionsRepositoryInterface->update($request, $AdminFunctions);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($AdminFunctions)
            {
                $state = $this->AdminFunctionsRepositoryInterface->destroy($AdminFunctions);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}