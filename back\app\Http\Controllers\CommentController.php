<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCommentRequest;
use App\Http\Requests\UpdateCommentRequest;
use App\Models\Comment;
use App\Repositories\Interfaces\CommentRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class CommentController extends Controller
{
    use ApiErrorResponse;

    private $CommentRepositoryInterface;

    public function __construct(CommentRepositoryInterface $CommentRepositoryInterface)
    {
        $this->CommentRepositoryInterface = $CommentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CommentRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCommentRequest $request)
    {
        $state = $this->CommentRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Comment)
    {
        $state = $this->CommentRepositoryInterface->show($Comment);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCommentRequest $request, Comment $Comment)
    {
        $state = $this->CommentRepositoryInterface->update($request, $Comment);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Comment)
    {
        $state = $this->CommentRepositoryInterface->destroy($Comment);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

}
