<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreWifiRoutersRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'name' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('wifi_routers')->ignore($Id) : 'unique:wifi_routers',
                    ],
                    'mac_address' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('wifi_routers')->ignore($Id) : 'unique:wifi_routers',
                    ],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The wifi routers name is required.',
                    'name.max' => 'The wifi routers name must not exceed 255 characters.',
                    'name.unique' => 'The wifi routers name is already taken. Please choose a different name.',
                    'mac_address.required' => 'The wifi routers mac_address is required.',
                    'mac_address.max' => 'The wifi routers mac_address must not exceed 255 characters.',
                    'mac_address.unique' => 'The wifi routers mac_address is already taken. Please choose a different name.',
                ];
            }
        
}