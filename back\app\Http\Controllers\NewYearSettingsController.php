<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\NewYearSettings;
use App\Http\Requests\StoreNewYearSettingsRequest;
use App\Http\Requests\UpdateNewYearSettingsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\NewYearSettingsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class NewYearSettingsController extends Controller
{
    use ApiErrorResponse;

    private $NewYearSettingsRepositoryInterface;


    public function __construct(NewYearSettingsRepositoryInterface $NewYearSettingsRepositoryInterface)
    {
        $this->NewYearSettingsRepositoryInterface = $NewYearSettingsRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->NewYearSettingsRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNewYearSettingsRequest $request)
    {
        $state = $this->NewYearSettingsRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($NewYearSettings)
    {
        $state = $this->NewYearSettingsRepositoryInterface->show($NewYearSettings);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNewYearSettingsRequest $request, NewYearSettings $NewYearSettings)
    {
        $state = $this->NewYearSettingsRepositoryInterface->update($request, $NewYearSettings);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($NewYearSettings)
    {
        $state = $this->NewYearSettingsRepositoryInterface->destroy($NewYearSettings);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
}
