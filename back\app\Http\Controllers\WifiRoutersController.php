<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\WifiRouters;
use App\Http\Requests\StoreWifiRoutersRequest;
use App\Http\Requests\UpdateWifiRoutersRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\WifiRoutersRepositoryInterface;
use App\Traits\ApiErrorResponse;

class WifiRoutersController extends Controller
{
    use ApiErrorResponse;

    private $WifiRoutersRepositoryInterface;

    
            public function __construct(WifiRoutersRepositoryInterface $WifiRoutersRepositoryInterface)
            {
                $this->WifiRoutersRepositoryInterface = $WifiRoutersRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->WifiRoutersRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreWifiRoutersRequest $request)
            {
                $state = $this->WifiRoutersRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($WifiRouters)
            {
                $state = $this->WifiRoutersRepositoryInterface->show($WifiRouters);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateWifiRoutersRequest $request, WifiRouters $WifiRouters)
            {
                $state = $this->WifiRoutersRepositoryInterface->update($request, $WifiRouters);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($WifiRouters)
            {
                $state = $this->WifiRoutersRepositoryInterface->destroy($WifiRouters);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}