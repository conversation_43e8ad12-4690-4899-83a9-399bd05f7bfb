<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSchoolCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'school_category' => [
                'required',
                'max:255',
                $Id ? Rule::unique('school_categories')->ignore($Id) : 'unique:school_categories',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'school_category.required' => 'The school category name is required.',
            'school_category.max' => 'The school category name must not exceed 255 characters.',
            'school_category.unique' => 'The school category name is already taken. Please choose a different name.',
        ];
    }
}
