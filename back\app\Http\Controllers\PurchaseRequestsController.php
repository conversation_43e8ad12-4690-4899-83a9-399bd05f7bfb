<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PurchaseRequests;
use App\Http\Requests\StorePurchaseRequestsRequest;
use App\Http\Requests\UpdatePurchaseRequestsRequest;
use App\Models\ManageClass;
use App\Models\ProcurementRequestFile;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\PurchaseRequestsRepositoryInterface;
use Illuminate\Support\Facades\{DB};
use App\Traits\ApiResponse;

class PurchaseRequestsController extends Controller
{
    use ApiResponse;

    private $PurchaseRequestsRepositoryInterface;
    public function __construct(PurchaseRequestsRepositoryInterface $PurchaseRequestsRepositoryInterface)
    {
        $this->PurchaseRequestsRepositoryInterface = $PurchaseRequestsRepositoryInterface;
    }
    public function index()
    {
        $state = $this->PurchaseRequestsRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }
    public function store(StorePurchaseRequestsRequest $request)
    {               
        try {
            DB::beginTransaction(); 
            $data = $this->PurchaseRequestsRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
            // return $this->generalError(config('constants.GENERAL_ERROR'));
        }        
    }
    public function update(UpdatePurchaseRequestsRequest $request)
    {                
        try {
            DB::beginTransaction(); 
             $purchaseRequest = PurchaseRequests::find($request->id);
             $state = $this->PurchaseRequestsRepositoryInterface->update($request, $purchaseRequest);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }  
    }
    public function destroy($PurchaseRequests)
    {
        try {
            DB::beginTransaction();
            $state = $this->PurchaseRequestsRepositoryInterface->destroy($PurchaseRequests);
            DB::commit();
            $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));           
        }
    }

    public function deleteProcurementItem()
    {
        try {
            DB::beginTransaction();
            $state = $this->PurchaseRequestsRepositoryInterface->deleteProcurementItem();
            DB::commit();
            $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));           
        }
    }

    public function updateRequestStatus(Request $request) {
        try {
            DB::beginTransaction();
            $state = $this->PurchaseRequestsRepositoryInterface->updateRequestStatus($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }

    public function teacherUpdatePurchaseRequestsStatus(Request $request) {
        try {
            DB::beginTransaction();
            $state = $this->PurchaseRequestsRepositoryInterface->teacherUpdatePurchaseRequestsStatus($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function deleteProcurementFile(Request $request) {
        try {
            DB::beginTransaction();
            ProcurementRequestFile::find($request->id)->delete();
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function getClassesByGrade(Request $request) {
        $organizationGradeLevel = $request->organization_grade_level_id;
        $result = ManageClass::select('id as value', 'name as label')->where('organization_grade_level_id', $organizationGradeLevel)->get();        
        return response()->json($result, Response::HTTP_OK);
    }
}
