<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEnrollmentStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'enrollment_status' => [
                'required',
                'max:255',
                $Id ? Rule::unique('enrollment_statuses')->ignore($Id) : 'unique:enrollment_statuses',
            ],
            'type' => ['required'],
            'status_code' => [
                'required',
                'max:255',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'enrollment_status.required' => 'The enrollment status name is required.',
            'type.required' => 'The Type is required.',
            'status_code.required' => 'The status code is required.',
            'status_code.max' => 'The status code must not exceed 255 characters.',
            'enrollment_status.max' => 'The enrollment status name must not exceed 255 characters.',
            'enrollment_status.unique' => 'The enrollment status name is already taken. Please choose a different name.',
        ];
    }
}
