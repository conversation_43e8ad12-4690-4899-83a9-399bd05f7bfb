<?php

if (! function_exists('frontEndConstants')) {
    function frontEndConstants($moduleName, $directory_path)
    {
        $directory = $directory_path.'constants/AppConstants.js';
        // Read the template content from the source file
        $filePath = resource_path('views/constants/index.text');
        $content = File::get($filePath);

        $placeholders = [
            '##MODULE_URL##' => strtoupper(str_replace(' ', '_', $moduleName)).'_API_URL',
            '##MODULE_URL_VALUE##' => strtolower(str_replace(' ', '-', $moduleName)),

            '##MODULE_SLICE##' => strtoupper(str_replace(' ', '_', $moduleName)),
            '##MODULE_SLICE_VALUE##' => str_replace(' ', '', ucwords($moduleName)).'Slice',
        ];

        $content .= "\n";
        foreach ($placeholders as $placeholder => $replacement) {
            $content = str_replace($placeholder, $replacement, $content);
        }

        file_put_contents($directory, $content, FILE_APPEND);
        if (file_exists($directory)) {
            return true;
        } else {
            return false;
        }
    }
}

if (! function_exists('frontEndSliceWithFolder')) {
    function frontEndSliceWithFolder($moduleName, $directory_path)
    {
        $directory = $directory_path.'store/slices/'.str_replace(' ', '', ucwords($moduleName));
        $fileName = 'manage'.str_replace(' ', '', ucwords($moduleName)).'Slice'.'.js';
        $filePath = $directory.'/'.$fileName;

        // Create the directories if they don't exist
        if (! File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePathSlice = resource_path('views/slices/index.text');
        $contentSlice = File::get($filePathSlice);

        $placeholders = [
            '##MODULE_SNAKE_NAME##' => Str::snake($moduleName),
            '##MODULE_NAME_WITH_SPACE##' => ucfirst($moduleName),
            '##MODULE_NAME##' => ucfirst(str_replace(' ', '', $moduleName)),
            '##MODULE_SLICE##' => strtoupper(str_replace(' ', '_', $moduleName)),
            '##MODULE_URL##' => strtoupper(str_replace(' ', '_', $moduleName)).'_API_URL',
            '##DIRECTORY_NAME##' => str_replace(' ', '', ucwords($moduleName)),
            '##LANGUAGE_VAR##' => str_replace(' ', '', Str::lower($moduleName)),
        ];

        foreach ($placeholders as $placeholder => $replacement) {
            $contentSlice = str_replace($placeholder, $replacement, $contentSlice);
        }

        // Create the file if it doesn't exist
        if (! File::exists($filePath)) {
            File::put($filePath, $contentSlice);

            // Check if the file was created
            if (File::exists($filePath)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }
}

if (! function_exists('frontEndComponentWithFolder')) {
    function frontEndComponentWithFolder($moduleName, $directory_path)
    {
        $directory = $directory_path.'components/Admin/'.str_replace(' ', '', ucwords($moduleName));
        $fileName = 'Index'.'.jsx';
        $filePath = $directory.'/'.$fileName;

        // Create the directories if they don't exist
        if (! File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePathSlice = resource_path('views/components/index.text');
        $contentSlice = File::get($filePathSlice);

        $placeholders = [
            '##MODULE_NAME_WITH_SPACE##' => ucfirst($moduleName),
            '##MODULE_NAME##' => ucfirst(str_replace(' ', '', $moduleName)),
            '##DIRECTORY_NAME##' => str_replace(' ', '', ucwords($moduleName)),
            '##LANGUAGE_VAR##' => str_replace(' ', '', Str::lower($moduleName)),

            '##MODULE_VIEW_URL##' => strtolower(str_replace(' ', '-', $moduleName)),
            '##MODULE_SLICE##' => strtoupper(str_replace(' ', '_', $moduleName)),
            '##MODULE_URL##' => strtoupper(str_replace(' ', '_', $moduleName)).'_API_URL',
        ];

        foreach ($placeholders as $placeholder => $replacement) {
            $contentSlice = str_replace($placeholder, $replacement, $contentSlice);
        }

        // Create the file if it doesn't exist
        if (! File::exists($filePath)) {
            File::put($filePath, $contentSlice);

            // Check if the file was created
            if (File::exists($filePath)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }
}

if (! function_exists('frontEndLanguageFileUpdate')) {
    function frontEndLanguageFileUpdate($moduleName, $directory_path)
    {
        // convert English Language
        // Path to the JSON file
        $filePath = $directory_path.'lang/locales/en_US.json';

        // Read the existing JSON content
        $jsonContent = file_get_contents($filePath);

        // Decode the JSON content into an associative array
        $data = json_decode($jsonContent, true);

        // Content to add
        $newContent = [
            'The '.Str::lower($moduleName).' name is required.' => 'The '.Str::lower($moduleName).' name is required.',
            'The '.Str::lower($moduleName).' name must not exceed 255 characters.' => 'The '.Str::lower($moduleName).' name must not exceed 255 characters.',
            'The '.Str::lower($moduleName).' name is already taken. Please choose a different name.' => 'The '.Str::lower($moduleName).' name is already taken. Please choose a different name.',
            ''.str_replace(' ', '', Str::lower($moduleName)).'' => [
                'add' => 'Add '.ucwords($moduleName).'',
                'edit' => 'Edit '.ucwords($moduleName).'',
                'view' => 'View '.ucwords($moduleName).'',
                'delete' => 'Delete '.ucwords($moduleName).'',
            ],
        ];

        // Merge the new content into the existing data
        $data = array_merge($data, $newContent);

        // Encode the modified data back to JSON
        $newJsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        // Write the updated JSON content back to the file
        file_put_contents($filePath, $newJsonContent);

        return true;
    }
}

// With this application will create frontend service file
//location is serc/services/related-folder/and file with some functions
if (! function_exists('frontEndService')) {
    function frontEndService($requestData)
    {
    }
}
// With this application will create frontend slice file
//location is serc/stoe/slice/related-folder and file with some functions
if (! function_exists('frontEndSlice')) {
    function frontEndSlice($requestData)
    {
    }
}
// With this application will create frontend index file
//location is serc/components/Admin/related-folder and file with some functions
if (! function_exists('frontFolderWithIndexFile')) {
    function frontFolderWithIndexFile($moduleName)
    {
        // Specify the directory path and file name
        // $directory = $directory_path.'components/Admin/'.str_replace(' ', '', ucwords($moduleName));
        // $fileName = 'Index.jsx';

        // // Create the directories if they don't exist
        // if (! File::isDirectory($directory)) {
        //     File::makeDirectory($directory, 0755, true, true);
        // }

        // $filePath = $directory.'/'.$fileName;

        // // Read the template content from the source file
        // $sourceFilePath = resource_path('views/modules/module-v1.text');
        // $content = File::get($sourceFilePath);

        // // Replace dynamic placeholders in the content
        // $placeholders = [
        //     '##MODULE_NAME_UPPER##' => 'NAME_OF_'.strtoupper(str_replace(' ', '_', $moduleName)).'_SLICE',
        //     '##SIMPLE_MODULE_NAME##' => str_replace(' ', '', ucwords($moduleName)),
        //     '##ADD_DRAWER_STATUS##' => ucwords($moduleName).'AddDrawerStatus',
        //     '##CREATE##' => ucwords($moduleName).'Create',
        //     '##GET##' => ucwords($moduleName).'Get',
        // ];

        // foreach ($placeholders as $placeholder => $replacement) {
        //     $content = str_replace($placeholder, $replacement, $content);
        // }

        // // Create the file
        // File::put($filePath, $content);

        // // Check if the file was created
        // if (File::exists($filePath)) {
        //     // File created successfully
        // } else {
        //     // Failed to create the file
        // }
    }
}

// With this application will create frontend modal file
//location is serc/components/Admin/related-folder and file with some functions
if (! function_exists('frontModalWithIndexFile')) {
    function frontModalWithIndexFile($moduleName, $directory_path)
    {
        // Specify the directory path and file name
        $directory = $directory_path.'components/Admin/'.str_replace(' ', '', ucwords($moduleName)).'/Modals';
        $fileName = 'index.jsx';

        // Create the directories if they don't exist
        if (! File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePath = $directory.'/'.$fileName;

        // Read the template content from the source file
        $sourceFilePath = resource_path('views/components/modals/index.text');
        $content = File::get($sourceFilePath);

        // Replace dynamic placeholders in the content
        $placeholders = [
            '##MODULE_NAME_WITH_SPACE##' => ucfirst($moduleName),
            '##MODULE_NAME##' => ucfirst(str_replace(' ', '', $moduleName)),
            '##DIRECTORY_NAME##' => str_replace(' ', '', ucwords($moduleName)),
            '##MODULE_SLICE##' => strtoupper(str_replace(' ', '_', $moduleName)),
        ];

        foreach ($placeholders as $placeholder => $replacement) {
            $content = str_replace($placeholder, $replacement, $content);
        }

        // Create the file
        File::put($filePath, $content);

        // Check if the file was created
        if (File::exists($filePath)) {
            // File created successfully
        } else {
            // Failed to create the file
        }
    }
}

if (! function_exists('frontModalWithViewFile')) {
    function frontModalWithViewFile($moduleName, $directory_path)
    {
        // Specify the directory path and file name
        $directory = $directory_path.'components/Admin/'.str_replace(' ', '', ucwords($moduleName)).'/Modals';
        $fileName = 'View.jsx';

        // Create the directories if they don't exist
        if (! File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePath = $directory.'/'.$fileName;

        // Read the template content from the source file
        $sourceFilePath = resource_path('views/components/modals/View.text');
        $content = File::get($sourceFilePath);

        // Replace dynamic placeholders in the content
        $placeholders = [
            '##MODULE_NAME_WITH_SPACE##' => ucfirst($moduleName),
            '##MODULE_NAME##' => ucfirst(str_replace(' ', '', $moduleName)),
            '##DIRECTORY_NAME##' => str_replace(' ', '', ucwords($moduleName)),
            '##MODULE_SLICE##' => strtoupper(str_replace(' ', '_', $moduleName)),
        ];

        foreach ($placeholders as $placeholder => $replacement) {
            $content = str_replace($placeholder, $replacement, $content);
        }

        // Create the file
        File::put($filePath, $content);

        // Check if the file was created
        if (File::exists($filePath)) {
            // File created successfully
        } else {
            // Failed to create the file
        }
    }
}

// With this application will create frontend constants
//location is serc/components/Admin/related-folder and file with some functions
if (! function_exists('frontModuleConstants')) {
    function frontModuleConstants($requestData)
    {
    }
}

if (! function_exists('frontModuleRootReducer')) {
    function frontModuleRootReducer($moduleName, $directory_path)
    {
        $directory = $directory_path.'store/rootReducer.js';
        $content = file_get_contents($directory);
        $moduleName = ucfirst(str_replace(' ', '', $moduleName));

        $importSlice = 'import manage'.$moduleName."Slice from './slices/".$moduleName.'/manage'.$moduleName."Slice';";
        $searchString = '//_import_slice_here';
        $pos = strpos($content, $searchString);
        $content = substr_replace($content, PHP_EOL.'    '.$importSlice, $pos + strlen($searchString), 0);

        $sliceName = 'manage'.$moduleName.'Slice,';
        $searchString = '//_Register_slice_after';
        $pos = strpos($content, $searchString);
        $content = substr_replace($content, PHP_EOL.'    '.$sliceName, $pos + strlen($searchString), 0);

        file_put_contents($directory, $content, LOCK_EX);
    }
}
