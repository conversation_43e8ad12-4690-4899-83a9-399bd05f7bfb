<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrganizationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'org_name' => 'required|max:200',
            'email' => 'required|email',
            'organization_id' => 'nullable',
            'short_name' => 'required',
            'org_number' => [
                'required',
            ],
            'organization_type_id' => 'required',
            'website' => 'required',
            'organization_category' => 'required',
            'school_type_id' => 'required',
            'school_category_id' => 'required',
            // 'timezone_code_id' => 'required',
            'addressId' => 'nullable',
            'address_type_id' => 'required',
            'province_id' => 'required',
            'city' => 'required',
            'postal_code' => 'required',
            'address_1' => 'required',
            'address_2' => 'nullable',
            'cell_phone' => [
                'required',
            ],
            'work_phone' => [
                'required',
            ],
            'home_phone' => [
                'required',
            ],
            'fax' => 'nullable',
        ];
    }
}
