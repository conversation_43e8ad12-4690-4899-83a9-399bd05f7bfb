<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\GuardianSiblingsReport;
use App\Http\Requests\StoreGuardianSiblingsReportRequest;
use App\Http\Requests\UpdateGuardianSiblingsReportRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\GuardianSiblingsReportRepositoryInterface;
use App\Traits\ApiErrorResponse;

class GuardianSiblingsReportController extends Controller
{
    use ApiErrorResponse;

    private $GuardianSiblingsReportRepositoryInterface;

    
            public function __construct(GuardianSiblingsReportRepositoryInterface $GuardianSiblingsReportRepositoryInterface)
            {
                $this->GuardianSiblingsReportRepositoryInterface = $GuardianSiblingsReportRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->GuardianSiblingsReportRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreGuardianSiblingsReportRequest $request)
            {
                $state = $this->GuardianSiblingsReportRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($GuardianSiblingsReport)
            {
                $state = $this->GuardianSiblingsReportRepositoryInterface->show($GuardianSiblingsReport);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateGuardianSiblingsReportRequest $request, GuardianSiblingsReport $GuardianSiblingsReport)
            {
                $state = $this->GuardianSiblingsReportRepositoryInterface->update($request, $GuardianSiblingsReport);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($GuardianSiblingsReport)
            {
                $state = $this->GuardianSiblingsReportRepositoryInterface->destroy($GuardianSiblingsReport);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }

            public function generateExcel(Request $request) {
                $result = $this->GuardianSiblingsReportRepositoryInterface->generateExcel($request);
                return $result;
            }
            
}