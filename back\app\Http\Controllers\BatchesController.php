<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreBatchesRequest;
use App\Http\Requests\UpdateBatchesRequest;
use App\Models\ChargeRate;
use App\Models\GradeLevel;
use App\Models\Guardian;
use App\Models\Invoice;
use App\Models\InvoiceBatch;
use App\Models\Organization;
use App\Models\OrganizationGradeLevel;
use App\Models\Payment;
use App\Models\RecurringCharge;
use App\Models\SchoolYear;
use App\Models\Student;
use App\Models\StudentCharge;
use App\Models\{
    StudentEnrollment,
    Logo
};
use App\Repositories\Interfaces\BatchesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PDF;
use Spatie\SimpleExcel\SimpleExcelReader;

class BatchesController extends Controller
{
    use ApiErrorResponse;

    private $BatchesRepositoryInterface;

    public function __construct(BatchesRepositoryInterface $BatchesRepositoryInterface)
    {
        $this->BatchesRepositoryInterface = $BatchesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->BatchesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBatchesRequest $request)
    {
        $state = $this->BatchesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    public function addStudentChargeInInvoice(Request $request)
    {
        return $state = $this->BatchesRepositoryInterface->addStudentChargeInInvoice($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Batches)
    {
        $state = $this->BatchesRepositoryInterface->show($Batches);

        return response()->json($state, Response::HTTP_OK);
    }

    public function batchInvoices(Request $Batches)
    {
        $state = $this->BatchesRepositoryInterface->batchInvoices($Batches);

        return response()->json($state, Response::HTTP_OK);
    }

    public function postBatch(Request $Batches)
    {
        $state = $this->BatchesRepositoryInterface->postBatch($Batches);

        return $state ? $this->successResponse(false, config('constants.POST_BATCH')) : $this->generalError(false, config('constants.GENERAL_ERROR'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBatchesRequest $request, InvoiceBatch $Batches)
    {
        $state = $this->BatchesRepositoryInterface->update($request, $Batches);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Batches)
    {
        $state = $this->BatchesRepositoryInterface->destroy($Batches);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function deleteStudentFromBatch(Request $Batches)
    {
        $state = $this->BatchesRepositoryInterface->deleteStudentFromBatch($Batches);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getGradeClasses(Request $Batches)
    {
        $state = $this->BatchesRepositoryInterface->getGradeClasses($Batches);

        return response()->json($state, Response::HTTP_OK);
    }

    public function viewStudentInvoice(Request $request)
    {
        // Retrieve the encoded ID from the query parameters
        $encodedId = $request->input('id');
        $school_year_id = $request->input('school_year_id');
        $organization_id = $request->input('organization_id');
        // Decode the ID using Laravel's decrypt method
        $id = Crypt::decryptString($encodedId);
        $queryResult = Invoice::select('*')
            ->with([
                'invoice_detail.charge_type',
            'invoice_detail.student_charge',
                'student.studentAddress',
                'student.guardians.guardianAddress',
                'invoice_batch',
                'invoiceTax.taxType.tax',
                'organization_grade_level.grade_level',
                'student.guardians' => function ($query) {
                    $query->whereIn('relationship_id', [Guardian::FATHER, Guardian::MOTHER]);
                    $query->where('invoice_and_payment_notification', 1);
                    $query->where('account_payable', 1);
                },
            ])
            ->where(['id' => $id])->first();

        if (isset($organization_id) && $organization_id != null) {
            $organization = Organization::with(['address'])->where('id', $organization_id)->first();
        } else {
            $studentData = Student::with(['enrollments.organizationGradeLevel', 'enrollments' => function ($query) {
                $query->orderBy('id', 'desc');
            }])->where('id', $queryResult?->student_id)->first()->toArray();
            $organization_id =  $studentData['enrollments'][0]['organization_grade_level']['organization_id'];
            $organization = Organization::with(['address.province'])->where('id', $organization_id)->first();
        }

        $guardians = $guardianAddresses = $invoiceCharges = $invoice_taxes = [];

        $studentData = $queryResult?->student?->toArray() ?? [];
        $totalTaxSum = 0;
        $invoiceTaxes = $queryResult->invoiceTax;
        if (isset($invoiceTaxes) && count($invoiceTaxes) > 0) {
            foreach ($invoiceTaxes as $key => $value) {
                $invoice_taxes[$key]['id'] = $value->id;
                $invoice_taxes[$key]['name'] = $value?->taxType?->tax?->name;
                $invoice_taxes[$key]['tax_amount'] = formatAmount($value?->tax_amount ?? 0);
                $invoice_taxes[$key]['tax_amount_without_format'] = $value?->tax_amount ?? 0;
                $invoice_taxes[$key]['tax_percentage'] = $value?->taxType->percentage;

                $totalTaxSum += $value?->tax_amount ?? 0;
            }
        }


        $guardiansName = $guardiansId = [];
        // Check if $queryResult and $queryResult->student exist
        // return $queryResult;
        if ($queryResult && count($studentData['student_address']) > 0) {
            if (isset($studentData['guardians']) && count($studentData['guardians']) > 0) {
                foreach ($studentData['guardians'] as $guardian_key => $guardian_value) {
                    $guardiansName[] = $guardian_value['full_name'] ?? '';
                    $guardiansId[] = $guardian_value['id'] ?? '';
                }
                $guardiansNameString = $guardiansIdString = '';

                // Join names with comma separated values
                if (!empty($guardiansName)) {
                    $guardiansNameString = implode(', ', $guardiansName);
                }

                // Join ids with comma separated values
                if (!empty($guardiansId)) {
                    $guardiansIdString = implode(', ', $guardiansId);
                }

                // Replace the last comma with " and " if there are multiple guardians
                if (count($guardiansName) > 1) {
                    $lastGuardianName = array_pop($guardiansName);
                    $guardiansNameString = implode(', ', $guardiansName) . ' and ' . $lastGuardianName;
                }

                if (count($guardiansId) > 1) {
                    $lastGuardianId = array_pop($guardiansId);
                    $guardiansIdString = implode(', ', $guardiansId) . ' and ' . $lastGuardianId;
                }
            }
            // Check if guardians exist
            $guardianAddresses = $studentData['student_address'][0] ?? [];
        }

        // Check if $queryResult and $queryResult->invoice_detail exist
        if ($queryResult && $queryResult->invoice_detail) {
            // Check if guardians exist
            $invoice_detail = $queryResult->invoice_detail->toArray();
            foreach ($invoice_detail as $charge_key => $charge_value) {

                $charge_type_tags = json_decode($charge_value['charge_type']['charge_type_tags'], true) ?? [];

                $charge_date = in_array('charge_date', $charge_type_tags)
                    ? $charge_value['student_charge']['charge_date']
                    : '';

                $invoiceCharges[] = [
                    'charge' => $charge_value['charge_type']['charge_type'],
                    'description' => $charge_value['student_charge']['description'],
                    'charge_date' => $charge_date,
                    'is_discount' => $charge_value['charge_type']['is_discount'],
                    'amount' => formatAmount($charge_value['charge_amount'] ?? 0),
                    'amountWithOutFormat' => $charge_value['charge_amount'] ?? 0,
                ];
            }

            // Sort $invoiceCharges by 'amountWithOutFormat' in descending order
            usort($invoiceCharges, function ($a, $b) {
                return $b['amountWithOutFormat'] <=> $a['amountWithOutFormat']; // Descending order
            });
        }

        // Using nullish coalescing to provide a default value if the property doesn't exist
        $guardianAddresses = $guardianAddresses ?? [];
        if ($guardianAddresses['province_id'] != null) {
            $province = DB::table('provinces')
                ->where('id', $guardianAddresses['province_id'])
                ->first();
        }

        $invoiceDate = Carbon::parse($queryResult->invoice_date); // Ensure it's a Carbon instance

        $studentPreviousPayment = Payment::where('payment_date', '<', $invoiceDate)
            ->where('student_id', $queryResult->student->id)
            ->sum('payment_amount');

        $studentPreviousInvoice = Invoice::where('invoice_date', '<', $invoiceDate)
            ->where('student_id', $queryResult->student->id)
            ->sum('invoice_subtotal');

        $balance = ($studentPreviousInvoice - $studentPreviousPayment) ?? 0;

        // Format the negative balance with parentheses
        $balance_fwd_with_previous_payment_without_format = $balance < 0 ? '(' . abs($balance) . ')' : $balance;
        $balance_fwd_with_previous_payment_with_format = formatAmount($balance);
        $logo = Logo::where('name', 'invoice_batches_logo')->first();

        $data = [
            'current_date' => Carbon::now()->format('Y-m-d'),
            'student' => $queryResult?->student?->full_name ?? '',
            'student_id' => $queryResult?->student?->id ?? '',
            'std_id' => $queryResult?->student?->student_id ?? '',
            'guardian' => $guardiansNameString ?? '',
            'guardian_id' => $guardiansIdString ?? '',
            'address_1' => $guardianAddresses['address_1'] ?? '',
            'address_2' => $guardianAddresses['address_2'] ?? '',
            'postal_code' => $guardianAddresses['postal_code'] ?? '',
            'city' => $guardianAddresses['city'] ?? '',
            'province' => $province->name ?? '',
            'cell_phone' => $guardianAddresses['cell_phone'] ?? '',
            'accountNumber' => '0000 ************',
            'invoiceNumber' => $queryResult?->invoice_no ?? '',
            'invoiceDate' => Carbon::parse($queryResult?->invoice_date)->format('Y-m-d'),

            'previous_charges' => formatAmount($studentPreviousInvoice ?? 0),
            'previous_payments' => formatAmount($studentPreviousPayment ?? 0),
            'balance_fwd' => formatAmount($queryResult?->balance_fwd ?? 0),
            'balance_fwd_with_previous_payment' => formatAmount(($queryResult?->balance_fwd - $studentPreviousPayment) ?? 0),
            'balance_fwd_with_previous_payment_without_format' => $balance_fwd_with_previous_payment_without_format,
            'balance_fwd_with_previous_payment_with_format' => $balance_fwd_with_previous_payment_with_format,
            'invoiceSubTotal' => formatAmount($queryResult?->invoice_subtotal ?? 0),
            'invoiceSubTotalWithOutFormat' => $queryResult?->invoice_subtotal ?? 0,
            'totalAmount' => formatAmount(($studentPreviousInvoice - $studentPreviousPayment) + $queryResult?->invoice_subtotal ?? 0),
            'totalAmountWithOutFormat' => ($studentPreviousInvoice - $studentPreviousPayment) + $queryResult?->invoice_subtotal ?? 0,
            'invoiceCharges' => $invoiceCharges ?? [],
            'invoice_taxes' => $invoice_taxes ?? [],
            'totalTaxSum' => $totalTaxSum ?? 0,

            'title' => $organization?->org_name ?? '',
            'organization_org_name' => $organization?->org_name ?? '',
            'organization_short_name' => $organization?->short_name ?? '',
            'organization_org_number' => $organization?->org_number ?? '',
            'organization_logo' => $organization?->org_logo ?? '',
            'invoice_batches_logo' => $logo?->logo ?? '',
            // 'organization_logo' => 'gobind-sarvar-brampton-logo.svg' ?? '',
            'organization_email' => $organization?->email ?? '',
            'organization_website' => $organization?->website ?? '',
            'organization_cell_phone' => $organization?->address?->cell_phone ?? '',
            'organization_address_1' => $organization?->address?->address_1 ?? '',
            'organization_address_2' => $organization?->address?->address_2 ?? '',
            'organization_city' => $organization?->address?->city ?? '',
            'organization_province' => $organization?->address?->province?->name ?? '',
            'organization_postal_code' => $organization?->address?->postal_code ?? '',

            'organization_account_phone_no' => $organization->account_phone_no ?? '',
            'organization_account_email' => $organization->account_email ?? '',
            'organization_account_address_1' => $organization?->account_address_1 ?? '',
            'organization_account_address_2' => $organization?->account_address_2 ?? '',
            'organization_account_city' => $organization?->account_city ?? '',
            'organization_account_postal_code' => $organization?->account_postal_code ?? '',
            'organization_account_website' => $organization->account_website ?? '',
            'organization_account_org_name' => $organization->account_org_name ?? '',
            'organization_account_gst_registration_no' => $organization->account_gst_registration_no ?? '',
            'organization_account_business_no' => $organization->account_business_no ?? '',


        ];

        // $pdf = PDF::loadView('invoices.student', $data);
        //NO module was created for system settings so I am usig raw query
        $invoices = DB::select('SELECT value FROM system_settings WHERE name = "invoice_template"');

        if (!empty($invoices)) {
            $invoice = $invoices[0]; // Accessing the first element of the array
            $pdf = PDF::loadView($invoice->value, $data);

            return $pdf->stream('student_invoice.pdf');
        } else {
            // Handle the case when no invoice template is found
            return 'No template assigned in system for Invoice.';
        }
        // return $pdf->download('student_invoice.pdf');
    }

    public function studentInvoice1()
    {
        $data = [
            'student' => 'ABC Student',
            'accountNumber' => '0000 ************',
            'invoiceNumber' => 'INV123',
            'invoiceDate' => now(),
            'totalAmount' => 100.00, // Replace with the actual invoice amount
        ];
        $pdf = PDF::loadView('invoices.student1', $data);

        return $pdf->stream('student_invoice.pdf');
        // return $pdf->download('student_invoice.pdf');
    }

    public function importBusTransportFee()
    {
        // return 'don`t run me again';
        // Correct path to the storage file
        // $path = storage_path('app/public/transportation-fee.xlsx');
        $path = storage_path('app/public/bus-fees-for-brampton.xlsx');

        // Create reader and trim header row
        $reader = SimpleExcelReader::create($path)->trimHeaderRow();

        // Get the rows from the Excel file
        $rows = $reader->getRows();
        $students = [];

        $defaultSchoolYear = 2;

        try {
            DB::beginTransaction();

            $startData = Carbon::parse('2024-09-01');
            $endData = Carbon::parse('2025-06-30');
            $current_date = $startData->copy(); // Start iterating from the start date
            $dateRanges = [];
            while ($current_date->lte($endData)) {
                $dateRanges[] = $current_date->format('Y-m-d');
                $current_date->addMonth(); // Move to the first day of the next month
            }
            $totalMonths = count($dateRanges);

            $currentDate = Carbon::now()->format('Y-m-d');

            foreach ($rows as $key => $row) {
                $student = Student::with(['enrollmentForSchoolYear'])->where('student_id', $row['SID'])->first();
                $organization_grade_level_id = $student?->enrollmentForSchoolYear?->organization_grade_level_id;

                if (!$student) {
                    // Log the issue and continue to the next row
                    Log::warning("Student not found with SID: {$row['SID']}");
                    continue;
                }

                if ($student->enrollmentForSchoolYear) {
                    // Return the student if no enrollment found
                } else {
                    Log::warning("Student enrollment not found with SID: {$row['SID']}");
                    return $student;
                }

                $chargeRate = ChargeRate::where('effective_from', '<=', $currentDate)
                    ->where('effective_to', '>=', $currentDate)
                    ->whereHas('charge_type', function ($query) {
                        $query->where('charge_type', 'Bus Fee');
                    })
                    ->where('organization_grade_level_id', $organization_grade_level_id)
                    ->first();

                if ($student) {
                    // if ($row['status'] == 1) {
                    $bus_fee = $row['bus_fee'] / $totalMonths;
                    foreach ($dateRanges as $key => $charge_date) {
                        $students[] = StudentCharge::UpdateOrCreate(
                            [
                                'charge_date' => $charge_date,
                                'student_id' => $student->id,
                                'charge_rate_id' => $chargeRate->id,
                                'school_year_id' => $defaultSchoolYear,
                            ],
                            [
                                'student_id' => $student->id,
                                'charge_rate_id' => $chargeRate->id,
                                'school_year_id' => $defaultSchoolYear,
                                'charge_date' => $charge_date,
                                'charge_amount' => $bus_fee,
                                'charge_status' => 'Pending',
                                'is_reversed' =>  0,
                                'description' => 'Transportation Fee',
                            ]
                        );
                    }
                    // } else {
                    //     $studentCharge = StudentCharge::where(
                    //         [
                    //             'student_id' => $student->id,
                    //             'charge_rate_id' => $chargeRate->id,
                    //             'school_year_id' => $defaultSchoolYear,
                    //         ]
                    //     );
                    //     $students[] = $studentCharge->first();
                    //     $studentCharge->delete();
                    // }
                }
            }

            // Process or return the rows as needed
            DB::commit();
            return $students;
        } catch (\Exception $exp) {
            Log::error($exp);
            DB::rollBack();

            return false;
        }
    }


    public function updateStudentDOB()
    {
        // Correct path to the storage file
        $path = storage_path('app/public/student-dob.xlsx');

        // Create reader and trim header row
        $reader = SimpleExcelReader::create($path)->trimHeaderRow();

        // Get the rows from the Excel file
        $rows = $reader->getRows();
        $students = [];

        try {
            DB::beginTransaction();

            $stdArray = [];
            foreach ($rows as $key => $row) {
                $student = Student::where('student_id', $row['SID'])->first();
                if (!$student) {
                    // Log the issue and continue to the next row
                    Log::warning("Student not found with SID: {$row['SID']}");
                    continue;
                }

                // Ensure DOB is formatted as a string before assignment
                $dob = $row['DOB'];
                if ($dob instanceof \DateTimeImmutable || $dob instanceof \DateTime) {
                    // Format DateTime object to Y-m-d
                    $dob = $dob->format('Y-m-d');
                }

                // Set birth_date on the student model
                $student->birth_date = $dob;
                $student->save();

                $stdArray[] = $student;
            }

            // Commit the transaction if everything is successful
            DB::commit();
            return $stdArray;
        } catch (\Exception $exp) {
            // Log the exception and roll back the transaction
            Log::error($exp);
            DB::rollBack();

            return false;
        }
    }


    public function viewPaymentReceipt(Request $request)
    {
        // Retrieve the encoded ID from the query parameters
        $encodedId = $request->input('id');
        // Decode the ID using Laravel's decrypt method
        $receiptData = Crypt::decryptString($encodedId);

        $receiptData = json_decode($receiptData, true); // true makes it an associative array

        $organization = Organization::with(['address'])->where('id', $receiptData[0]['organization_id'])->first();


        // Using nullish coalescing to provide a default value if the property doesn't exist
        $guardianAddresses = $receiptData[0]['guardianAddresses'] ?? [];
        $data = [
            'current_date' => Carbon::now()->format('Y-m-d'),
            'paymentDate' => $receiptData[0]['payment_date'] ?? '',
            'paymentNumber' => $receiptData[0]['receipt_number'] ?? '',
            'receiptData' => $receiptData ?? [],
            'guardian' => $receiptData[0]['guardian'] ?? '',
            'guardian_id' => $receiptData[0]['guardian_id'] ?? '',
            'address_1' => $guardianAddresses['address_1'] ?? '',
            'address_2' => $guardianAddresses['address_2'] ?? '',
            'postal_code' => $guardianAddresses['postal_code'] ?? '',
            'city' => $guardianAddresses['city'] ?? '',
            'cell_phone' => $guardianAddresses['cell_phone'] ?? '',

            'title' => $organization?->org_name ?? '',
            'organization_org_name' => $organization?->org_name ?? '',
            'organization_short_name' => $organization?->short_name ?? '',
            'organization_org_number' => $organization?->org_number ?? '',
            'organization_logo' => 'gss-invoice-logo.png' ?? '',
            'organization_email' => $organization?->email ?? '',
            'organization_website' => $organization?->website ?? '',
            'organization_cell_phone' => $organization?->address?->cell_phone ?? '',
            'organization_address_1' => $organization?->address?->address_1 ?? '',
            'organization_address_2' => $organization?->address?->address_2 ?? '',
            'organization_city' => $organization?->address?->city ?? '',
            'organization_postal_code' => $organization?->address?->postal_code ?? '',

            'organization_account_phone_no' => $organization->account_phone_no ?? '',
            'organization_account_email' => $organization->account_email ?? '',
            'organization_account_address_1' => $organization?->account_address_1 ?? '',
            'organization_account_address_2' => $organization?->account_address_2 ?? '',
            'organization_account_city' => $organization?->account_city ?? '',
            'organization_account_postal_code' => $organization?->account_postal_code ?? '',
            'organization_account_website' => $organization->account_website ?? '',
            'organization_account_org_name' => $organization->account_org_name ?? '',
            'organization_account_gst_registration_no' => $organization->account_gst_registration_no ?? '',
            'organization_account_business_no' => $organization->account_business_no ?? '',

        ];

        // $pdf = PDF::loadView('invoices.student', $data);
        //NO module was created for system settings so I am usig raw query
        $invoices = DB::select('SELECT value FROM system_settings WHERE name = "receipt_template"');

        if (!empty($invoices)) {
            $invoice = $invoices[0]; // Accessing the first element of the array
            $pdf = PDF::loadView($invoice->value, $data);

            return $pdf->stream('Payment_Receipt.pdf');
        } else {
            // Handle the case when no invoice template is found
            return 'No template assigned in system for Invoice.';
        }
        // return $pdf->download('student_invoice.pdf');
    }


    public function updateStudentEnrollment()
    {
        // Correct path to the storage file
        $path = storage_path('app/public/std-grade-replacing.xlsx');

        // Create reader and trim header row
        $reader = SimpleExcelReader::create($path)->trimHeaderRow();

        // Get the rows from the Excel file
        $rows = $reader->getRows();
        $students = [];

        try {
            DB::beginTransaction();

            $stdArray = [];
            foreach ($rows as $key => $row) {
                $student = Student::where('student_id', $row['SID'])->first();
                if (!$student) {
                    // Log the issue and continue to the next row
                    Log::warning("Student not found with SID: {$row['SID']}");
                    continue;
                }

                $studentEnrollment = StudentEnrollment::where('student_id', $student->id)->orderBy('id', 'desc')->first();
                $gradeLevel = OrganizationGradeLevel::with('grade_level')
                    ->whereHas('grade_level', function ($query) use ($row) {
                        $query->where('grade_level', $row['GradeInNewFile']);
                    })->first();
                $previousGradeLevel = OrganizationGradeLevel::with('grade_level')
                    ->whereHas('grade_level', function ($query) use ($row) {
                        $query->where('grade_level', $row['EnrollInSystem']);
                    })->first();

                $recurringChargePrevious = RecurringCharge::with('charge_rate')->whereHas('charge_rate', function ($query) use ($previousGradeLevel) {
                    $query->where('organization_grade_level_id', $previousGradeLevel->id);
                })->where('student_id', $studentEnrollment->student_id)->get();

                $studentChargePrevious = StudentCharge::with('charge_rate')->whereHas('charge_rate', function ($query) use ($previousGradeLevel) {
                    $query->where('organization_grade_level_id', $previousGradeLevel->id);
                })->where('student_id', $studentEnrollment->student_id)->get();


                $recurringChargePrevious->map(
                    function ($rcp) use ($gradeLevel) {
                        $chargeRate = ChargeRate::where('organization_grade_level_id', $gradeLevel->id)->where('charge_type_id', $rcp->charge_rate->charge_type_id)->first();
                        $rcp->update(['charge_rate_id' => $chargeRate->id]);
                    }
                );
                $studentChargePrevious->map(
                    function ($scp) use ($gradeLevel) {
                        $chargeRate = ChargeRate::where('organization_grade_level_id', $gradeLevel->id)->where('charge_type_id', $scp->charge_rate->charge_type_id)->first();
                        $scp->update(['charge_rate_id' => $chargeRate->id]);
                    }
                );

                if ($studentEnrollment) {
                    // Format DateTime object to Y-m-d
                    $studentEnrollment->update([
                        'organization_grade_level_id' => $gradeLevel->id,
                    ]);
                }

                $stdArray[] = $student;
            }

            // Commit the transaction if everything is successful
            DB::commit();
            return $stdArray;
        } catch (\Exception $exp) {
            // Log the exception and roll back the transaction
            Log::error($exp);
            DB::rollBack();

            return false;
        }
    }
}
