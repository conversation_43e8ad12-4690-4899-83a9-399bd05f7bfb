<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LegalStatus;
use App\Http\Requests\StoreLegalStatusRequest;
use App\Http\Requests\UpdateLegalStatusRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\LegalStatusRepositoryInterface;
use App\Traits\ApiErrorResponse;

class LegalStatusController extends Controller
{
    use ApiErrorResponse;

    private $LegalStatusRepositoryInterface;

    
            public function __construct(LegalStatusRepositoryInterface $LegalStatusRepositoryInterface)
            {
                $this->LegalStatusRepositoryInterface = $LegalStatusRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->LegalStatusRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreLegalStatusRequest $request)
            {
                $state = $this->LegalStatusRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($LegalStatus)
            {
                $state = $this->LegalStatusRepositoryInterface->show($LegalStatus);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateLegalStatusRequest $request, LegalStatus $LegalStatus)
            {
                $state = $this->LegalStatusRepositoryInterface->update($request, $LegalStatus);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($LegalStatus)
            {
                $state = $this->LegalStatusRepositoryInterface->destroy($LegalStatus);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}