<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateIncidentReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'incident_type_id' => 'required',
            'title' => [
                'required',
                'max:255',
                $Id ? Rule::unique('incidents')->ignore($Id) : 'unique:incidents',
            ],
            'incident_date' => 'required|date',
            'time_frame' => 'required',
            // 'description' => 'required',
            // 'location_description' => 'required',
        ];
    }
    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'incident_type_id.required' => 'The incident type ID is required.',
            'title.required' => 'The incident report title is required.',
            'title.max' => 'The incident report title must not exceed 255 characters.',
            'title.unique' => 'The incident report title is already taken. Please choose a different title.',
            'incident_date.required' => 'The incident date is required.',
            'incident_date.date' => 'The incident date must be a valid date.',
            'time_frame.required' => 'The time frame is required.',
            // 'description.required' => 'The description is required.',
            // 'location_description.required' => 'The location description is required.',
        ];
    }
}
