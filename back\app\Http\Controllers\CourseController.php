<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCourseRequest;
use App\Http\Requests\UpdateCourseRequest;
use App\Models\Course;
use App\Repositories\Interfaces\CourseRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class CourseController extends Controller
{
    use ApiErrorResponse;

    private $CourseRepositoryInterface;

    public function __construct(CourseRepositoryInterface $CourseRepositoryInterface)
    {
        $this->CourseRepositoryInterface = $CourseRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CourseRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCourseRequest $request)
    {
        $state = $this->CourseRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Course)
    {
        $state = $this->CourseRepositoryInterface->show($Course);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCourseRequest $request, Course $Course)
    {
        $state = $this->CourseRepositoryInterface->update($request, $Course);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Course)
    {
        $state = $this->CourseRepositoryInterface->destroy($Course);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
    //
    public function get_all_course_category()
    {
        $state = $this->CourseRepositoryInterface->get_all_course_category();    
        return response()->json($state, Response::HTTP_OK);
    }
}
