<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AcademicReports;
use App\Http\Requests\StoreAcademicReportsRequest;
use App\Http\Requests\UpdateAcademicReportsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\AcademicReportsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class AcademicReportsController extends Controller
{
    use ApiErrorResponse;

    private $AcademicReportsRepositoryInterface;

    
            public function __construct(AcademicReportsRepositoryInterface $AcademicReportsRepositoryInterface)
            {
                $this->AcademicReportsRepositoryInterface = $AcademicReportsRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->AcademicReportsRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreAcademicReportsRequest $request)
            {
                $state = $this->AcademicReportsRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($AcademicReports)
            {
                $state = $this->AcademicReportsRepositoryInterface->show($AcademicReports);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateAcademicReportsRequest $request, AcademicReports $AcademicReports)
            {
                $state = $this->AcademicReportsRepositoryInterface->update($request, $AcademicReports);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($AcademicReports)
            {
                $state = $this->AcademicReportsRepositoryInterface->destroy($AcademicReports);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}