<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageRouteRequest;
use App\Http\Requests\UpdateManageRouteRequest;
use App\Models\ManageRoute;
use App\Repositories\Interfaces\ManageRouteRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ManageRouteController extends Controller
{
    use ApiErrorResponse;

    private $ManageRouteRepositoryInterface;

    public function __construct(ManageRouteRepositoryInterface $ManageRouteRepositoryInterface)
    {
        $this->ManageRouteRepositoryInterface = $ManageRouteRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageRouteRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageRouteRequest $request)
    {
        $state = $this->ManageRouteRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageRoute)
    {
        $state = $this->ManageRouteRepositoryInterface->show($ManageRoute);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageRouteRequest $request, ManageRoute $ManageRoute)
    {
        $state = $this->ManageRouteRepositoryInterface->update($request, $ManageRoute);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageRoute)
    {
        $state = $this->ManageRouteRepositoryInterface->destroy($ManageRoute);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
