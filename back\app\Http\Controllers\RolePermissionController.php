<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRolePermissionRequest;
use App\Http\Requests\UpdateRolePermissionRequest;
use App\Models\RolePermission;
use App\Repositories\Interfaces\RolePermissionRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class RolePermissionController extends Controller
{
    use ApiErrorResponse;

    private $RolePermissionRepositoryInterface;

    public function __construct(RolePermissionRepositoryInterface $RolePermissionRepositoryInterface)
    {
        $this->RolePermissionRepositoryInterface = $RolePermissionRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->RolePermissionRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }


    public function getExcludeOrganizationRole(Request $request)
    {
        $state = $this->RolePermissionRepositoryInterface->getExcludeOrganizationRole($request);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRolePermissionRequest $request)
    {
        $state = $this->RolePermissionRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(RolePermission $RolePermission)
    {
        return $RolePermission ?? [];
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRolePermissionRequest $request, RolePermission $RolePermission)
    {
        $state = $this->RolePermissionRepositoryInterface->update($request, $RolePermission);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($RolePermission)
    {
        $state = $this->RolePermissionRepositoryInterface->destroy($RolePermission);

        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
