<?php

namespace App\Http\Controllers;

use App\Models\LeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreLeaveApplicationRequest;
use App\Http\Requests\UpdateLeaveApplicationRequest;
use App\Repositories\Interfaces\LeaveApplicationRepositoryInterface;

class LeaveApplicationController extends Controller
{
    use ApiErrorResponse;

    private $LeaveApplicationRepositoryInterface;

    public function __construct(LeaveApplicationRepositoryInterface $LeaveApplicationRepositoryInterface)
    {
        $this->LeaveApplicationRepositoryInterface = $LeaveApplicationRepositoryInterface;
    }
    public function index()
    {            
        $result = $this->LeaveApplicationRepositoryInterface->index();
        return response()->json($result, Response::HTTP_OK);
    }
    public function store(StoreLeaveApplicationRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->LeaveApplicationRepositoryInterface->store($request);
            DB::commit();
            return $this->successResponse(false, config('constants.LEAVE_APPLIED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }       
    }
    public function show($LeaveApplication)
    {
        // $state = $this->LeaveApplicationRepositoryInterface->show($LeaveApplication);
        // return response()->json($state, Response::HTTP_OK);
    }
    public function update(UpdateLeaveApplicationRequest $request, LeaveRequest $LeaveApplication)
    {
        try {
            DB::beginTransaction();
            $this->LeaveApplicationRepositoryInterface->update($request, $LeaveApplication);
            DB::commit();
            return $this->successResponse(false, config('constants.LEAVE_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }       
    }
    public function destroy($LeaveApplication)
    {
        try {
            DB::beginTransaction();
            $this->LeaveApplicationRepositoryInterface->destroy($LeaveApplication);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }        
        // $state = $this->LeaveApplicationRepositoryInterface->destroy($LeaveApplication);
        // if ($state != 1 && $state == 23000) {
        //     return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        // } else {
        //     return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        // }
    }

    public function getSelectedYearGuardianStudents(Request $request)
    {        
        $state = $this->LeaveApplicationRepositoryInterface->getSelectedYearGuardianStudents($request);
        return response()->json($state, Response::HTTP_OK);
    }
}
