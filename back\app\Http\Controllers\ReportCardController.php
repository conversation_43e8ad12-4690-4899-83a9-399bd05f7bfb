<?php

namespace App\Http\Controllers;

use App\Models\ReportCard;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\StoreReportCardRequest;
use App\Http\Requests\UpdateReportCardRequest;
use App\Repositories\Interfaces\ReportCardRepositoryInterface;
use Illuminate\Support\Facades\Crypt;
use PDF;

class ReportCardController extends Controller
{
    use ApiResponse;

    private $ReportCardRepositoryInterface;


    public function __construct(ReportCardRepositoryInterface $ReportCardRepositoryInterface)
    {
        $this->ReportCardRepositoryInterface = $ReportCardRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ReportCardRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     * StoreReportCardRequest
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR_MESSAGE'));
        }
    }

    public function saveStudentAssessment(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardRepositoryInterface->saveStudentAssessment($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR_MESSAGE'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($ReportCard)
    {
        $state = $this->ReportCardRepositoryInterface->show($ReportCard);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateReportCardRequest $request, ReportCard $ReportCard)
    {
        $state = $this->ReportCardRepositoryInterface->update($request, $ReportCard);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ReportCard)
    {
        $state = $this->ReportCardRepositoryInterface->destroy($ReportCard);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }

    // TODO:: will remove and its routes will be removed
    public function downloadReportCardPdf(Request $request)
    {
        // Retrieve the encoded ID from the query parameters
        // $encodedId = $request->input('id');

        // Decode the ID using Laravel's decrypt method
        // $id = Crypt::decryptString($encodedId);

        // $pdfData = [
        //     'first_signature' => 'Hamza',
        //     'second_signature' => 'Shahid',
        // ];
        // $pdf = PDF::loadView('reportCard.Kindergarten_Progress_Report', $pdfData);

        // return $pdf->stream('ReportCard.pdf');

        // $data = $request->all();
        // $invoice_file_name = $data['invoice_file_name'];

        // return $fileUrl = asset('storage/app/public/Contracts/' . $invoice_file_name);
    }

    // TODO:: END

    public function updateCourseCategoriesDetail(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardRepositoryInterface->updateCourseCategoriesDetail($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }
}
