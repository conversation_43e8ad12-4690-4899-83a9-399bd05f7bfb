<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRoleRequest;
use App\Http\Requests\UpdateRoleRequest;
use App\Models\Permission;
use App\Repositories\Interfaces\RoleRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class RoleController extends Controller
{
    use ApiErrorResponse;

    /**
     * Display a listing of the resource.
     */
    private $RoleRepositoryInterface;

    public function __construct(RoleRepositoryInterface $RoleRepositoryInterface)
    {
        $this->RoleRepositoryInterface = $RoleRepositoryInterface;
    }

    public function index(Request $request)
    {
        $state = $this->RoleRepositoryInterface->index($request->query());

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRoleRequest $request)
    {

        $state = $this->RoleRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRoleRequest $request, Permission $permission)
    {
        $state = $this->RoleRepositoryInterface->update($request, $permission);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($permission)
    {
        $state = $this->RoleRepositoryInterface->destroy($permission);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    /**
     * organization roles
     */
    public function getOranizationRoles(Request $request)
    {
        $result['data'] = $this->RoleRepositoryInterface->getOranizationRoles($request);

        return response()->json($result, Response::HTTP_OK);
    }
}
