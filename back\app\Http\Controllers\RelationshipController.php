<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRelationshipRequest;
use App\Http\Requests\UpdateRelationshipRequest;
use App\Models\Relationship;
use App\Repositories\Interfaces\RelationshipRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class RelationshipController extends Controller
{
    use ApiErrorResponse;

    private $RelationshipRepositoryInterface;

    public function __construct(RelationshipRepositoryInterface $RelationshipRepositoryInterface)
    {
        $this->RelationshipRepositoryInterface = $RelationshipRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->RelationshipRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRelationshipRequest $request)
    {
        $state = $this->RelationshipRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Relationship)
    {
        $state = $this->RelationshipRepositoryInterface->show($Relationship);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRelationshipRequest $request, Relationship $Relationship)
    {
        $state = $this->RelationshipRepositoryInterface->update($request, $Relationship);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Relationship)
    {
        $state = $this->RelationshipRepositoryInterface->destroy($Relationship);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
