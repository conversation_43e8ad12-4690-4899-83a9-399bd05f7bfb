<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSiblingReportRequest;
use App\Http\Requests\UpdateSiblingReportRequest;
use App\Models\SiblingReport;
use App\Repositories\Interfaces\SiblingReportRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SiblingReportController extends Controller
{
    use ApiErrorResponse;

    private $SiblingReportRepositoryInterface;

    public function __construct(SiblingReportRepositoryInterface $SiblingReportRepositoryInterface)
    {
        $this->SiblingReportRepositoryInterface = $SiblingReportRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SiblingReportRepositoryInterface->index();
        // return $state;
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSiblingReportRequest $request)
    {
        $state = $this->SiblingReportRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SiblingReport)
    {
        $state = $this->SiblingReportRepositoryInterface->show($SiblingReport);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSiblingReportRequest $request, SiblingReport $SiblingReport)
    {
        $state = $this->SiblingReportRepositoryInterface->update($request, $SiblingReport);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SiblingReport)
    {
        $state = $this->SiblingReportRepositoryInterface->destroy($SiblingReport);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function exportSiblingReport(Request $request)
    {
        $result = $this->SiblingReportRepositoryInterface->exportSiblingReport($request);
        return $result;
    }
}
