<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreChargesRatesRequest;
use App\Http\Requests\UpdateChargesRatesRequest;
use App\Models\ChargeRate;
use App\Repositories\Interfaces\ChargesRatesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ChargesRatesController extends Controller
{
    use ApiErrorResponse;

    private $ChargesRatesRepositoryInterface;

    public function __construct(ChargesRatesRepositoryInterface $ChargesRatesRepositoryInterface)
    {
        $this->ChargesRatesRepositoryInterface = $ChargesRatesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ChargesRatesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreChargesRatesRequest $request)
    {
        $state = $this->ChargesRatesRepositoryInterface->store($request);
        return $state['status'] ? $this->successResponse(false, config('constants.DATA_INSERTED')) : $this->generalWarning(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     */
    public function show($ChargesRates)
    {
        $state = $this->ChargesRatesRepositoryInterface->show($ChargesRates);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateChargesRatesRequest $request, ChargeRate $ChargesRates)
    {
        // return $request;
        $state = $this->ChargesRatesRepositoryInterface->update($request, $ChargesRates);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ChargesRates)
    {
        $state = $this->ChargesRatesRepositoryInterface->destroy($ChargesRates);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function getGradeCharges(Request $request)
    {
        $result['data'] = $this->ChargesRatesRepositoryInterface->getGradeCharges($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getChargeRateExist()
    {
        $state = $this->ChargesRatesRepositoryInterface->getChargeRateExist();

        return response()->json($state, Response::HTTP_OK);
    }
}
