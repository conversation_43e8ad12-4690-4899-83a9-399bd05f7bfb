<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\IncidentActionCode;
use App\Http\Requests\StoreIncidentActionCodeRequest;
use App\Http\Requests\UpdateIncidentActionCodeRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\IncidentActionCodeRepositoryInterface;
use App\Traits\ApiErrorResponse;

class IncidentActionCodeController extends Controller
{
    use ApiErrorResponse;

    private $IncidentActionCodeRepositoryInterface;


    public function __construct(IncidentActionCodeRepositoryInterface $IncidentActionCodeRepositoryInterface)
    {
        $this->IncidentActionCodeRepositoryInterface = $IncidentActionCodeRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->IncidentActionCodeRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreIncidentActionCodeRequest $request)
    {
        $state = $this->IncidentActionCodeRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($IncidentActionCode)
    {
        $state = $this->IncidentActionCodeRepositoryInterface->show($IncidentActionCode);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateIncidentActionCodeRequest $request, IncidentActionCode $IncidentActionCode)
    {
        $state = $this->IncidentActionCodeRepositoryInterface->update($request, $IncidentActionCode);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($IncidentActionCode)
    {
        $state = $this->IncidentActionCodeRepositoryInterface->destroy($IncidentActionCode);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
}
