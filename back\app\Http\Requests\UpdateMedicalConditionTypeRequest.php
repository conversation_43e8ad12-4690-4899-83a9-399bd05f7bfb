<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateMedicalConditionTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'medical_condition_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('medical_condition_types')->ignore($Id) : 'unique:medical_condition_types',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'medical_condition_type.required' => 'The medical condition type name is required.',
            'medical_condition_type.max' => 'The medical condition type name must not exceed 255 characters.',
            'medical_condition_type.unique' => 'The medical condition type name is already taken. Please choose a different name.',
        ];
    }
}
