<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentConversationRequest;
use App\Http\Requests\UpdateStudentConversationRequest;
use App\Models\StudentConversation;
use App\Repositories\Interfaces\StudentConversationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class StudentConversationController extends Controller
{
    use ApiErrorResponse;

    private $StudentConversationRepositoryInterface;

    public function __construct(StudentConversationRepositoryInterface $StudentConversationRepositoryInterface)
    {
        $this->StudentConversationRepositoryInterface = $StudentConversationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->StudentConversationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentConversationRequest $request)
    {
        $state = $this->StudentConversationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($StudentConversation)
    {
        $state = $this->StudentConversationRepositoryInterface->show($StudentConversation);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentConversationRequest $request, StudentConversation $StudentConversation)
    {
        $state = $this->StudentConversationRepositoryInterface->update($request, $StudentConversation);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentConversation)
    {
        $state = $this->StudentConversationRepositoryInterface->destroy($StudentConversation);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
