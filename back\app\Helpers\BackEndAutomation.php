<?php

// With this application will create frontend constants
//location is serc/components/Admin/related-folder and file with some functions
if (!function_exists('backendModuleRouteApi')) {
    function backendModuleRouteApi($moduleName)
    {
        $directory = base_path('routes/ApiService');
        $fileName = Str::ucfirst(Str::camel($moduleName)) . 'Api.php';

        // Create the directories if they don't exist
        if (!File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePath = $directory . '/' . $fileName;

        // Generate the content for the new file
        $slug = str_replace(' ', '-', strtolower($moduleName));
        $controllerName = Str::ucfirst(Str::camel($moduleName));

        $content = <<<EOT
        <?php

        use Illuminate\Support\Facades\Route;

        Route::group([
            'middleware' => 'auth:api',
            'prefix' => 'auth',
        ], function () {
            Route::resource('$slug', '{$controllerName}Controller');
            Route::post('get-all-$slug', '{$controllerName}Controller@index');
        });
        EOT;

        // Create the file and put the content
        File::put($filePath, $content);

        // Check if the file was created
        if (File::exists($filePath)) {
            // File was created successfully
        } else {
            // Failed to create the file
        }
    }
}

// With this application will create frontend constants
//location is serc/components/Admin/related-folder and file with some functions
if (!function_exists('createRepositoryInterface')) {
    function createRepositoryInterface($moduleName)
    {
        $directory = base_path('app/Repositories/Interfaces');
        $fileName = Str::ucfirst(Str::camel($moduleName)) . 'RepositoryInterface.php';

        // Create the directories if they don't exist
        if (!File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePath = $directory . '/' . $fileName;

        // Generate the content for the new file
        $InterfaceName = Str::ucfirst(Str::camel($moduleName)) . 'RepositoryInterface';

        $content = '<?php' . PHP_EOL . PHP_EOL .
            'namespace App\Repositories\Interfaces;' . PHP_EOL . PHP_EOL .
            'interface ' . $InterfaceName . PHP_EOL .
            '{' . PHP_EOL .
            '    public function index();' . PHP_EOL .
            '    public function store($requestData);' . PHP_EOL .
            '    public function show($requestData);' . PHP_EOL .
            '    public function update($requestData, $packageTrail);' . PHP_EOL .
            '    public function destroy($requestData);' . PHP_EOL .
            '}' . PHP_EOL;

        // Create the file and put the content
        File::put($filePath, $content);

        // Check if the file was created
        if (File::exists($filePath)) {
            // File was created successfully
        } else {
            // Failed to create the file
        }
    }
}

// With this application will create Repository


if (!function_exists('createRepository')) {
    function createRepository($moduleName)
    {
        $directory = base_path('app/Repositories');
        $fileName = Str::ucfirst(Str::camel($moduleName)) . 'Repository.php';

        // Create the directories if they don't exist
        if (!File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        $filePath = $directory . '/' . $fileName;

        // Generate the content for the new file
        $InterfaceName = Str::ucfirst(Str::camel($moduleName)) . 'Repository';
        $modelName = Str::ucfirst(Str::camel($moduleName));
        $resourceName = Str::ucfirst(Str::camel($moduleName)) . 'Resource';

        $content = '<?php

        namespace App\Repositories;
        use App\Repositories\Interfaces\\' . $InterfaceName . 'Interface;
        use App\Models\\' . Str::ucfirst(Str::camel($moduleName)) . ';
        use Illuminate\Support\Facades\DB;
        use Illuminate\Support\Facades\Crypt;
        use Illuminate\Support\Facades\Log;
        use App\Http\Resources\\' . $resourceName . ';


        class ' . $InterfaceName . ' implements ' . $InterfaceName . 'Interface
        {
            public function index()
            {
                $data = request();

                $perPage = intval($data["perPage"] ?? 100000);
                $page = max(1, intval($data["page"] ?? 1));
                $sorting = isset($data["sorting"]) && is_array($data["sorting"]) ? $data["sorting"] : [];
                $filter = isset($data["filter"]) && is_array($data["filter"]) ? $data["filter"] : [];

                $queryResult = ' . $modelName . '::select("*", "id as value", "name as label");

                // Apply filters
                foreach ($filter as $key => $value) {
                    if ($value !== null) {
                        $stringRepresentation = is_array($value) ? implode(", ", $value) : $value;
                        $queryResult->where($key, "like", "%$stringRepresentation%");
                    }
                }

                // Apply sorting
                if (!empty($sorting)) {
                    foreach ($sorting as $field => $order) {
                        $field = $field == "undefined" ? "created_at" : $field;
                        $queryResult->orderBy($field, $order);
                    }
                } else {
                    $queryResult->orderBy("created_at", "desc");
                }

                $query = $queryResult->paginate($perPage, ["*"], "page", $page);

                $response = [
                    "data" => ' . $resourceName . '::collection($query),
                    "pagination" => [
                        "current_page" => $query->currentPage(),
                        "last_page" => $query->lastPage(),
                        "per_page" => $query->perPage(),
                        "total" => $query->total(),
                    ],
                ];
                return $response;
            }

            public function store($requestData)
            {
                try {
                    DB::beginTransaction();
                    $query = ' . $modelName . '::create([
                        "name"    => $requestData->name,
                    ]);
                    DB::commit();
                    return true;
                } catch (\Exception $e) {
                    Log::error($e);
                    DB::rollBack();
                    return false;
                }
            }

            public function show($requestData)
            {
                $id = Crypt::decryptString($requestData);
                return $data = ' . $modelName . '::find($id);
            }

            public function update($requestData, $packageTrail)
            {
                try {
                    DB::beginTransaction();
                    $query = ' . $modelName . '::find($requestData->id);

                    if (!$query) {
                        // Handle the case where the user with the given ID does not exist
                        return false;
                    }
                        $query->update([
                            "name" => $requestData->name,
                        ]);
                    DB::commit();
                    return true;
                } catch (\Exception $e) {
                    Log::error($e);
                    DB::rollBack();
                    return false;
                }
            }

            public function destroy($requestData)
            {
                try {
                    DB::beginTransaction();
                    $query = ' . $modelName . '::find($requestData);
                    if (!$query) {
                        throw new \Exception("Resource not found");
                    }
                    $query->delete();
                    DB::commit();
                    return true;
                } catch (\Exception $e) {
                    Log::error($e);
                    DB::rollBack();
                    if ($e->getCode() == "23000") {
                        // Extract the error message from the exception
                        $errorMessage = $e->getMessage();
                        return $e->getCode();
                    } else {
                        return false;
                    }
                }
            }
        }';

        // Create the file and put the content
        File::put($filePath, $content);

        // Check if the file was created
        if (File::exists($filePath)) {
            // File was created successfully
        } else {
            // Failed to create the file
        }
    }

    //location is serc/components/Admin/related-folder and file with some functions
    if (!function_exists('createResource')) {
        function createResource($moduleName)
        {
            $directory = base_path('app/Http/Resources');
            $fileName = Str::ucfirst(Str::camel($moduleName)) . 'Resource.php';

            // Create the directories if they don't exist
            if (!File::isDirectory($directory)) {
                File::makeDirectory($directory, 0755, true, true);
            }

            $filePath = $directory . '/' . $fileName;

            // Generate the content for the new file
            $resourceName = Str::ucfirst(Str::camel($moduleName)) . 'Resource';

            $content = '<?php namespace App\Http\Resources;' . PHP_EOL .
                'use Illuminate\Http\Request;' . PHP_EOL  .
                'use Illuminate\Http\Resources\Json\JsonResource;' . PHP_EOL  .

                'class ' . $resourceName . ' extends JsonResource' . PHP_EOL  .
                '{' . PHP_EOL .

                'public function toArray(Request $request): array' . PHP_EOL .
                '{
                        $data = [
                            "id" => $this->id,
                            "enc_id" => $this->enc_id,
                            "name" => $this->name,
                            "value" => $this->id,
                            "label" => $this->name,
                        ];' . PHP_EOL .
                'return $data;' . PHP_EOL .
                '}' . PHP_EOL .
                '}';


            // Create the file and put the content
            File::put($filePath, $content);

            // Check if the file was created
            if (File::exists($filePath)) {
                // File was created successfully
            } else {
                // Failed to create the file
            }
        }
    }


    if (!function_exists('addModuleSeeder')) {
        function addModuleSeeder($moduleName, $icon, $id, $lastDigit, $parent, $application_id, $isGroupTitle)
        {
            $directory_path = base_path('database/seeders/');

            $directory = $directory_path . 'ModuleSeeder.php';
            $permissionDirectory = $directory_path . 'ModulePermissionSeeder.php';
            $rolePermissionDirectory = $directory_path . 'RolePermissionSeeder.php';

            $content = file_get_contents($directory);
            $permissionContent = file_get_contents($permissionDirectory);
            $rolePermissionContent = file_get_contents($rolePermissionDirectory);

            $path = strtolower(str_replace(' ', '-', $moduleName));
            $moduleName = ucfirst(str_replace(' ', ' ', $moduleName));
            $lastDigit = $lastDigit != null ? $lastDigit : 'null';
            $isGroupTitle = isset($isGroupTitle) ? $isGroupTitle : 0;
            // module
            $importSlice = "[
                'id' => " . $id . ",
                'application_id' => " . $application_id . ",
                'name' => '" . $moduleName . "',
                'display_name' => '" . $moduleName . "',
                'path' => '" . $path . "',
                'icon' => '" . $icon . "',
                'breadcrumb' => false,
                'isGroupTitle' => " . $isGroupTitle . ",
                'parent_id' => " . $lastDigit . ",
                'user_id' => 1,
                'communication_avail' => 0,
                'order' => 11,
                'visible_status' => 1,
                'email_notification' => 0,
                'sms_notification' => 0,
            ],";
            $searchString = '//_add_more_module_here';
            $pos = strpos($content, $searchString);
            $content = substr_replace($content, PHP_EOL . '    ' . $importSlice, $pos + strlen($searchString), 0);
            file_put_contents($directory, $content, LOCK_EX);

            // module permission
            $permissionImportSlice = "
            ['module_id' => " . $id . ", 'permission_id' => 1],
            ['module_id' => " . $id . ", 'permission_id' => 2],
            ['module_id' => " . $id . ", 'permission_id' => 3],
            ['module_id' => " . $id . ", 'permission_id' => 4],
            ['module_id' => " . $id . ", 'permission_id' => 5],
            ['module_id' => " . $id . ", 'permission_id' => 6],";
            $permissionSearchString = '//_add_module_permission';
            $pos = strpos($permissionContent, $permissionSearchString);
            $permissionContent = substr_replace($permissionContent, PHP_EOL . '    ' . $permissionImportSlice, $pos + strlen($permissionSearchString), 0);
            file_put_contents($permissionDirectory, $permissionContent, LOCK_EX);

            // role permission
            if ($parent !== null) {
                $rolePermissionImportSlice = '
                        "' . $parent . '-' . $id . '",
                        "' . $parent . '-' . $id . '-Delete",
                        "' . $parent . '-' . $id . '-Update",
                        "' . $parent . '-' . $id . '-Create",
                        "' . $parent . '-' . $id . '-View",
                        "' . $parent . '-' . $id . '-Import",
                        "' . $parent . '-' . $id . '-Export",
                ';
            } else {
                $rolePermissionImportSlice = '
                "' . $id . '",
                ';
            }

            $rolePermissionSearchString = '/*put_contentHere*/';
            $pos = strpos($rolePermissionContent, $rolePermissionSearchString);
            $rolePermissionContent = substr_replace($rolePermissionContent, PHP_EOL . '    ' . $rolePermissionImportSlice, $pos + strlen($permissionSearchString), 0);
            file_put_contents($rolePermissionDirectory, $rolePermissionContent, LOCK_EX);
        }
    }
}
