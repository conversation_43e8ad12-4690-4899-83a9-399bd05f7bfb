<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAddressTypeRequest;
use App\Http\Requests\UpdateAddressTypeRequest;
use App\Models\AddressType;
use App\Repositories\Interfaces\AddressTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class AddressTypeController extends Controller
{
    use ApiErrorResponse;

    private $AddressTypeRepositoryInterface;

    public function __construct(AddressTypeRepositoryInterface $AddressTypeRepositoryInterface)
    {
        $this->AddressTypeRepositoryInterface = $AddressTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->AddressTypeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAddressTypeRequest $request)
    {
        $state = $this->AddressTypeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($AddressType)
    {
        $state = $this->AddressTypeRepositoryInterface->show($AddressType);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAddressTypeRequest $request, AddressType $AddressType)
    {
        $state = $this->AddressTypeRepositoryInterface->update($request, $AddressType);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($AddressType)
    {
        $state = $this->AddressTypeRepositoryInterface->destroy($AddressType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
