<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TransportationReport;
use App\Http\Requests\StoreTransportationReportRequest;
use App\Http\Requests\UpdateTransportationReportRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\TransportationReportRepositoryInterface;
use App\Traits\ApiErrorResponse;

class TransportationReportController extends Controller
{
    use ApiErrorResponse;

    private $TransportationReportRepositoryInterface;

    
            public function __construct(TransportationReportRepositoryInterface $TransportationReportRepositoryInterface)
            {
                $this->TransportationReportRepositoryInterface = $TransportationReportRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->TransportationReportRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreTransportationReportRequest $request)
            {
                $state = $this->TransportationReportRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($TransportationReport)
            {
                $state = $this->TransportationReportRepositoryInterface->show($TransportationReport);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateTransportationReportRequest $request, TransportationReport $TransportationReport)
            {
                $state = $this->TransportationReportRepositoryInterface->update($request, $TransportationReport);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($TransportationReport)
            {
                $state = $this->TransportationReportRepositoryInterface->destroy($TransportationReport);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}