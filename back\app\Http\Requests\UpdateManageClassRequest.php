<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateManageClassRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->get('id');

        return [
            'name' => [
                'required',
                'max:255',
                $id ? Rule::unique('manage_classes')->where(function ($query) use ($id) {
                    $query->where('school_year_id', $this->school_year_id)->where('id', '<>', $id);
                }) : Rule::unique('manage_classes')->where('school_year_id', $this->school_year_id),
            ],
            'organization_grade_level_id' => 'required|numeric',
            'school_year_id' => 'required|numeric',
            'school_room_id' => 'required|numeric',
            'class_date' => 'required',
            'is_same_room' => 'nullable|boolean',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'required' => 'The :attribute field is required.',
            'numeric' => 'The :attribute must be a number.',
            'max' => 'The :attribute must not exceed :max characters.',
            'unique' => 'The :attribute is already taken. Please choose a different :attribute.',
            'boolean' => 'The :attribute field must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'class name',
            'organization_grade_level_id' => 'organization grade level',
            'school_year_id' => 'school year',
            'school_room_id' => 'school room',
            'class_date' => 'class date',
            'is_same_room' => 'is same room',
        ];
    }
}
