<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreModuleRequest;
use App\Http\Requests\UpdateModuleRequest;
use App\Models\Module;
use App\Repositories\Interfaces\ModuleRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class ModuleController extends Controller
{
    use ApiErrorResponse;

    private $moduleRepository;

    public function __construct(ModuleRepositoryInterface $moduleRepository)
    {
        $this->moduleRepository = $moduleRepository;
    }

    //
    public function index()
    {

        $modules = $this->moduleRepository->index();

        return response()->json($modules, Response::HTTP_OK);
    }

    //
    public function store(StoreModuleRequest $request)
    {
        $state = $this->moduleRepository->store($request);

        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : '';
    }

    public function update(UpdateModuleRequest $request, Module $requestData)
    {
        $state = $this->moduleRepository->update($request, $requestData);

        return $state ? $this->successResponse($state, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $module = $this->moduleRepository->show($id);

        return response()->json($module, Response::HTTP_OK);
    }

    // Implement other methods in your controller
    public function get_module_with_permission()
    {
        $modules = Module::with('childModules')
            ->whereNull('parent_id')
            ->where('visible_status', '=', 1)
            ->get();
        $nestedModules = $this->transformToNestedStructure($modules);

        return response()->json($nestedModules, Response::HTTP_OK);
    }

    private function transformToNestedStructure($modules, $parentKey = null)
    {
        $nestedModules = [];
        foreach ($modules as $key => $module) {
            $currentKey = $parentKey ? $parentKey.'-'.$module->id : (string) $module->id;
            $nestedModule = [
                'title' => $module->name,
                'key' => $currentKey,
                'children' => $this->transformToNestedStructure($module->childModules, $currentKey),
                // Add other module fields as needed
            ];

            $permissions = $module->permissions->pluck('name')->toArray();

            // Add permissions directly under the module
            if (! empty($permissions)) {
                foreach ($permissions as $permission) {
                    $permissionKey = $currentKey.'-'.$permission;
                    $nestedModule['children'][] = [
                        'title' => $permission,
                        'key' => $permissionKey,
                        'children' => [], // No children for permissions
                    ];
                }
            }

            $nestedModules[] = $nestedModule;
        }

        return $nestedModules;
    }

    //
    public function get_applications()
    {
        $application = DB::table('applications')->select('name as label', 'id as value')->get();

        return response()->json($application, Response::HTTP_OK);
    }

    // Implement other methods in your controller
    // public function get_module_with_permission()
    // {
    //     $modules = $this->moduleRepository->get_module_with_permission();
    //     $result = [];

    //     foreach ($modules as $module) {
    //         $parentModule = [
    //             'title' => $module['name'],
    //             'key' => (string) $module['id'],
    //             'children' => [],
    //         ];

    //         foreach ($module['permissions'] as $permission) {
    //             $permissionKey = $module['id'].'-'.$permission['id'];
    //             $permissionItem = [
    //                 'title' => $permission['name'],
    //                 'key' => $permissionKey,
    //             ];

    //             $parentModule['children'][] = $permissionItem;
    //         }

    //         $result[] = $parentModule;
    //     }

    //     return response()->json($result, Response::HTTP_OK);
    // }
}
