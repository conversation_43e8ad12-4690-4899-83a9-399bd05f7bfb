<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAbsentReasonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'absent_reason' => [
                'required',
                'max:255',
                $Id ? Rule::unique('absent_reasons')->ignore($Id) : 'unique:absent_reasons',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'absent_reason.required' => 'The absent reason name is required.',
            'absent_reason.max' => 'The absent reason name must not exceed 255 characters.',
            'absent_reason.unique' => 'The absent reason name is already taken. Please choose a different name.',
        ];
    }
}
