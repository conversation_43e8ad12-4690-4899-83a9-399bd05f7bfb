<?php

namespace App\Http\Requests\Assignment;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTopicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $id = $this->get('id');

        return [
            'name' => ['required', 'min:3',
                // $id ? Rule::unique('topics')->ignore($id) : 'unique:topics',
            ],
            'grade_level_id' => 'required',
            'course_id' => 'required',
        ];
    }

    /**some validation messages  */
    public function messages()
    {
        //TODO: languag translate messages
        return [
            'name.required' => 'The name field is required.',
            'name.min' => 'The name must be at least :min characters.',
            // 'name.unique' => 'The name has already been taken.',
            'grade_level_id.required' => 'The grade level field is required.',
            'course_id.required' => 'The course field is required.',
        ];
    }
}
