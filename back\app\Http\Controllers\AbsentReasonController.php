<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAbsentReasonRequest;
use App\Http\Requests\UpdateAbsentReasonRequest;
use App\Models\AbsentReason;
use App\Repositories\Interfaces\AbsentReasonRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;


class AbsentReasonController extends Controller
{
    use ApiErrorResponse;

    private $AbsentReasonRepositoryInterface;

    public function __construct(AbsentReasonRepositoryInterface $AbsentReasonRepositoryInterface)
    {
        $this->AbsentReasonRepositoryInterface = $AbsentReasonRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->AbsentReasonRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAbsentReasonRequest $request)
    {
        $state = $this->AbsentReasonRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($AbsentReason)
    {
        $state = $this->AbsentReasonRepositoryInterface->show($AbsentReason);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAbsentReasonRequest $request, AbsentReason $AbsentReason)
    {
        $state = $this->AbsentReasonRepositoryInterface->update($request, $AbsentReason);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($AbsentReason)
    {
        $state = $this->AbsentReasonRepositoryInterface->destroy($AbsentReason);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
