<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDahsboardRequest;
use App\Http\Requests\UpdateDahsboardRequest;
use App\Models\Dahsboard;

class DahsboardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDahsboardRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Dahsboard $dahsboard)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDahsboardRequest $request, Dahsboard $dahsboard)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Dahsboard $dahsboard)
    {
        //
    }
}
