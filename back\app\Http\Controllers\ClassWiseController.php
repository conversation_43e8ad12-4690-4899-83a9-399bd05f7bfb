<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ClassWise;
use App\Http\Requests\StoreClassWiseRequest;
use App\Http\Requests\UpdateClassWiseRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\ClassWiseRepositoryInterface;
use App\Traits\ApiErrorResponse;

class ClassWiseController extends Controller
{
    use ApiErrorResponse;

    private $ClassWiseRepositoryInterface;


    public function __construct(ClassWiseRepositoryInterface $ClassWiseRepositoryInterface)
    {
        $this->ClassWiseRepositoryInterface = $ClassWiseRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ClassWiseRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreClassWiseRequest $request)
    {
        $state = $this->ClassWiseRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ClassWise)
    {
        $state = $this->ClassWiseRepositoryInterface->show($ClassWise);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateClassWiseRequest $request, ClassWise $ClassWise)
    {
        $state = $this->ClassWiseRepositoryInterface->update($request, $ClassWise);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ClassWise)
    {
        $state = $this->ClassWiseRepositoryInterface->destroy($ClassWise);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }

    public function exportStudentsClassWise(Request $request)
    {
        $result = $this->ClassWiseRepositoryInterface->exportStudentsClassWise($request);
        return $result;
    }
}
