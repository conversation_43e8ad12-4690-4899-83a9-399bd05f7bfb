<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAddressTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'address_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('address_types')->ignore($Id) : 'unique:address_types',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'address_type.required' => 'The address type name is required.',
            'address_type.max' => 'The address type name must not exceed 255 characters.',
            'address_type.unique' => 'The address type name is already taken. Please choose a different name.',
        ];
    }
}
