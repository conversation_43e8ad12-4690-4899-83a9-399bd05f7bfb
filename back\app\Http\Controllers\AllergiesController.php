<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAllergiesRequest;
use App\Http\Requests\UpdateAllergiesRequest;
use App\Models\Allergy;
use App\Repositories\Interfaces\AllergiesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class AllergiesController extends Controller
{
    use ApiErrorResponse;

    private $AllergiesRepositoryInterface;

    public function __construct(AllergiesRepositoryInterface $AllergiesRepositoryInterface)
    {
        $this->AllergiesRepositoryInterface = $AllergiesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->AllergiesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAllergiesRequest $request)
    {
        $state = $this->AllergiesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Allergy)
    {
        $state = $this->AllergiesRepositoryInterface->show($Allergy);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAllergiesRequest $request, Allergy $Allergies)
    {
        $state = $this->AllergiesRepositoryInterface->update($request, $Allergies);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Allergy)
    {
        $state = $this->AllergiesRepositoryInterface->destroy($Allergy);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
