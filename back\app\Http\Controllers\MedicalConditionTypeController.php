<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMedicalConditionTypeRequest;
use App\Http\Requests\UpdateMedicalConditionTypeRequest;
use App\Models\MedicalConditionType;
use App\Repositories\Interfaces\MedicalConditionTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class MedicalConditionTypeController extends Controller
{
    use ApiErrorResponse;

    private $MedicalConditionTypeRepositoryInterface;

    public function __construct(MedicalConditionTypeRepositoryInterface $MedicalConditionTypeRepositoryInterface)
    {
        $this->MedicalConditionTypeRepositoryInterface = $MedicalConditionTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->MedicalConditionTypeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMedicalConditionTypeRequest $request)
    {
        $state = $this->MedicalConditionTypeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($MedicalConditionType)
    {
        $state = $this->MedicalConditionTypeRepositoryInterface->show($MedicalConditionType);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMedicalConditionTypeRequest $request, MedicalConditionType $MedicalConditionType)
    {
        $state = $this->MedicalConditionTypeRepositoryInterface->update($request, $MedicalConditionType);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($MedicalConditionType)
    {
        $state = $this->MedicalConditionTypeRepositoryInterface->destroy($MedicalConditionType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
