<?php

namespace App\Http\Controllers;

use App\Models\LeaveRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreLeaveRequestRequest;
use App\Http\Requests\AddNewLeaveRequestRequest;
use App\Http\Requests\UpdateLeaveRequestRequest;
use App\Repositories\Interfaces\LeaveRequestRepositoryInterface;

class LeaveRequestController extends Controller
{
    use ApiErrorResponse;

    private $LeaveRequestRepositoryInterface;

    public function __construct(LeaveRequestRepositoryInterface $LeaveRequestRepositoryInterface)
    {
        $this->LeaveRequestRepositoryInterface = $LeaveRequestRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->LeaveRequestRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLeaveRequestRequest $request)
    {
        $state = $this->LeaveRequestRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    public function addNewLeaveRequest(AddNewLeaveRequestRequest $request)
    {        
        try {
            DB::beginTransaction();
            $this->LeaveRequestRepositoryInterface->addNewLeaveRequest($request);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }

    // public function get_attendance_codes_for_leave(Request $request)
    // {
    //     $state = $this->LeaveRequestRepositoryInterface->get_attendance_codes_for_leave();

    //     return response()->json($state, Response::HTTP_OK);
    // }

    /**
     * Display the specified resource.
     */
    public function show($LeaveRequest)
    {
        $state = $this->LeaveRequestRepositoryInterface->show($LeaveRequest);

        return response()->json($state, Response::HTTP_OK);
    }
    public function update(UpdateLeaveRequestRequest $request, LeaveRequest $LeaveRequest)
    {
        try {
            DB::beginTransaction();
            $this->LeaveRequestRepositoryInterface->update($request, $LeaveRequest);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
        
        // if ($state['status'] == 'Reject') {
        //     $msg = config('constants.REJECT_APPLIED_LEAVE');
        // } elseif ($state['status'] == 'Approve') {
        //     $msg = config('constants.APPROVED_APPLIED_LEAVE');
        // }
        // return $state ? $this->successResponse(false, $msg) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($LeaveRequest)
    {
        $state = $this->LeaveRequestRepositoryInterface->destroy($LeaveRequest);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
