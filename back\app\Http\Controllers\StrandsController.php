<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Strands;
use App\Http\Requests\StoreStrandsRequest;
use App\Http\Requests\UpdateStrandsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\StrandsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class StrandsController extends Controller
{
    use ApiErrorResponse;

    private $StrandsRepositoryInterface;


    public function __construct(StrandsRepositoryInterface $StrandsRepositoryInterface)
    {
        $this->StrandsRepositoryInterface = $StrandsRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->StrandsRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStrandsRequest $request)
    {
        $state = $this->StrandsRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Strands)
    {
        $state = $this->StrandsRepositoryInterface->show($Strands);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStrandsRequest $request, Strands $Strands)
    {
        $state = $this->StrandsRepositoryInterface->update($request, $Strands);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Strands)
    {
        $state = $this->StrandsRepositoryInterface->destroy($Strands);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }

    public function updateStrandsSort(Request $Strands)
    {
        $state = $this->StrandsRepositoryInterface->updateStrandsSort($Strands);
        return response()->json($state, Response::HTTP_OK);
    }
}
