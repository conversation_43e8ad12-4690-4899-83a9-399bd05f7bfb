<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CustodialArrangement;
use App\Http\Requests\StoreCustodialArrangementRequest;
use App\Http\Requests\UpdateCustodialArrangementRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\CustodialArrangementRepositoryInterface;
use App\Traits\ApiErrorResponse;

class CustodialArrangementController extends Controller
{
    use ApiErrorResponse;

    private $CustodialArrangementRepositoryInterface;

    
            public function __construct(CustodialArrangementRepositoryInterface $CustodialArrangementRepositoryInterface)
            {
                $this->CustodialArrangementRepositoryInterface = $CustodialArrangementRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->CustodialArrangementRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreCustodialArrangementRequest $request)
            {
                $state = $this->CustodialArrangementRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($CustodialArrangement)
            {
                $state = $this->CustodialArrangementRepositoryInterface->show($CustodialArrangement);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateCustodialArrangementRequest $request, CustodialArrangement $CustodialArrangement)
            {
                $state = $this->CustodialArrangementRepositoryInterface->update($request, $CustodialArrangement);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($CustodialArrangement)
            {
                $state = $this->CustodialArrangementRepositoryInterface->destroy($CustodialArrangement);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}