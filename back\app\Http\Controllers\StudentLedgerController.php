<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentLedgerRequest;
use App\Http\Requests\UpdateStudentLedgerRequest;
use App\Models\StudentLedger;
use App\Repositories\Interfaces\StudentLedgerRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class StudentLedgerController extends Controller
{
    use ApiErrorResponse;

    private $StudentLedgerRepositoryInterface;

    public function __construct(StudentLedgerRepositoryInterface $StudentLedgerRepositoryInterface)
    {
        $this->StudentLedgerRepositoryInterface = $StudentLedgerRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $requestData)
    {
        return $state = $this->StudentLedgerRepositoryInterface->index($requestData);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentLedgerRequest $request)
    {
        $state = $this->StudentLedgerRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($StudentLedger)
    {
        $state = $this->StudentLedgerRepositoryInterface->show($StudentLedger);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentLedgerRequest $request, StudentLedger $StudentLedger)
    {
        $state = $this->StudentLedgerRepositoryInterface->update($request, $StudentLedger);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentLedger)
    {
        $state = $this->StudentLedgerRepositoryInterface->destroy($StudentLedger);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getGradeLevelStudents(Request $requestData)
    {
        $state = $this->StudentLedgerRepositoryInterface->getGradeLevelStudents($requestData);
        return response()->json($state, Response::HTTP_OK);
    }

}
