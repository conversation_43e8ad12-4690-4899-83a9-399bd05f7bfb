<?php

namespace App\Http\Requests\Assignment;

use Illuminate\Foundation\Http\FormRequest;

class StoreAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $id = $this->get('id');

        return [
            'title' => ['required', 'min:3'],
            'topic_id' => 'required',
            'assignment_type_id' => 'required',
            'due_date' => 'required',
            'assignment_status' => 'required',
            'schedule_date' => 'required_if:assignment_status,scheduled|before:due_date',
            'class_student_ids' => 'required',
        ];
    }

    /**some validation messages  */
    public function messages()
    {
        //TODO: languag translate messages
        return [
            'title.required' => 'The title field is required.',
            'title.min' => 'The title must be at least :min characters.',
            'topic_id.required' => 'Topic is required',
            'assignment_type_id.required' => 'Assignment Type is required',
            'due_date.required' => 'Due date is required',
            'assignment_status.required' => 'Assignment status date is required',
            'schedule_date.required_if' => 'Schedule date is required when assignment status is scheduled.',
            'schedule_date.before' => 'Schedule date must be before due date.',
            'class_student_ids.required' => 'Select at least one student',
        ];
    }
}
