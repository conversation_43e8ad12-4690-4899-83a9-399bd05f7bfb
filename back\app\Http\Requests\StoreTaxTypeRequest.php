<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTaxTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tax_id' => 'required',
            'percentage' => 'required',
            'effective_from' => 'required',
            'effective_to' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tax_id.required' => 'The tax ID is required.',
            'percentage.required' => 'The percentage is required.',
            'effective_from.required' => 'The effective from date is required.',
            'effective_to.required' => 'The effective to date is required.',
        ];
    }
}
