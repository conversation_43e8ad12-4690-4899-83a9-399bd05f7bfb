<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOrganizationTypeRequest;
use App\Http\Requests\UpdateOrganizationTypeRequest;
use App\Models\OrganizationType;
use App\Repositories\Interfaces\OrganizationTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class OrganizationTypeController extends Controller
{
    use ApiErrorResponse;

    private $OrganizationTypeRepositoryInterface;

    public function __construct(OrganizationTypeRepositoryInterface $OrganizationTypeRepositoryInterface)
    {
        $this->OrganizationTypeRepositoryInterface = $OrganizationTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->OrganizationTypeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOrganizationTypeRequest $request)
    {
        $state = $this->OrganizationTypeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(OrganizationType $OrganizationType)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateOrganizationTypeRequest $request, OrganizationType $OrganizationType)
    {
        $state = $this->OrganizationTypeRepositoryInterface->update($request, $OrganizationType);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($OrganizationType)
    {
        $state = $this->OrganizationTypeRepositoryInterface->destroy($OrganizationType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
