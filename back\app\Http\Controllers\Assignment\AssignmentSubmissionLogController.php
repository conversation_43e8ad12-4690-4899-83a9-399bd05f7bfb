<?php

namespace App\Http\Controllers\Assignment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Assignment\StoreAssignmentSubmissionLogRequest;
use App\Http\Requests\Assignment\UpdateAssignmentSubmissionLogRequest;
use App\Models\Assignment\AssignmentSubmissionLog;

class AssignmentSubmissionLogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAssignmentSubmissionLogRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAssignmentSubmissionLogRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(AssignmentSubmissionLog $assignmentSubmissionLog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(AssignmentSubmissionLog $assignmentSubmissionLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAssignmentSubmissionLogRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateAssignmentSubmissionLogRequest $request, AssignmentSubmissionLog $assignmentSubmissionLog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(AssignmentSubmissionLog $assignmentSubmissionLog)
    {
        //
    }
}
