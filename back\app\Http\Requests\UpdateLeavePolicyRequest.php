<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLeavePolicyRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'name' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('leave_policies')->ignore($Id) : 'unique:leave_policies',
                    ],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The leave policy name is required.',
                    'name.max' => 'The leave policy name must not exceed 255 characters.',
                    'name.unique' => 'The leave policy name is already taken. Please choose a different name.',
                ];
            }
        
}