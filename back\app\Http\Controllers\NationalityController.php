<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreNationalityRequest;
use App\Http\Requests\UpdateNationalityRequest;
use App\Models\Nationality;
use App\Repositories\Interfaces\NationalityRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class NationalityController extends Controller
{
    use ApiErrorResponse;

    private $NationalityRepositoryInterface;

    public function __construct(NationalityRepositoryInterface $NationalityRepositoryInterface)
    {
        $this->NationalityRepositoryInterface = $NationalityRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->NationalityRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNationalityRequest $request)
    {
        $state = $this->NationalityRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Nationality)
    {
        $state = $this->NationalityRepositoryInterface->show($Nationality);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateNationalityRequest $request, Nationality $Nationality)
    {
        $state = $this->NationalityRepositoryInterface->update($request, $Nationality);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Nationality)
    {
        $state = $this->NationalityRepositoryInterface->destroy($Nationality);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
