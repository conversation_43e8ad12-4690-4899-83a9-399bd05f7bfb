<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePaymentModeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'payment_mode' => [
                'required',
                'max:255',
                $Id ? Rule::unique('payment_modes')->ignore($Id) : 'unique:payment_modes',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'payment_mode.required' => 'The payment mode name is required.',
            'payment_mode.max' => 'The payment mode name must not exceed 255 characters.',
            'payment_mode.unique' => 'The payment mode name is already taken. Please choose a different name.',
        ];
    }
}
