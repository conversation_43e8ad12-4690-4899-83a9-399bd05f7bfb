<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CustodialMaritalStatus;
use App\Http\Requests\StoreCustodialMaritalStatusRequest;
use App\Http\Requests\UpdateCustodialMaritalStatusRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\CustodialMaritalStatusRepositoryInterface;
use App\Traits\ApiErrorResponse;

class CustodialMaritalStatusController extends Controller
{
    use ApiErrorResponse;

    private $CustodialMaritalStatusRepositoryInterface;

    
            public function __construct(CustodialMaritalStatusRepositoryInterface $CustodialMaritalStatusRepositoryInterface)
            {
                $this->CustodialMaritalStatusRepositoryInterface = $CustodialMaritalStatusRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->CustodialMaritalStatusRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreCustodialMaritalStatusRequest $request)
            {
                $state = $this->CustodialMaritalStatusRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($CustodialMaritalStatus)
            {
                $state = $this->CustodialMaritalStatusRepositoryInterface->show($CustodialMaritalStatus);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateCustodialMaritalStatusRequest $request, CustodialMaritalStatus $CustodialMaritalStatus)
            {
                $state = $this->CustodialMaritalStatusRepositoryInterface->update($request, $CustodialMaritalStatus);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($CustodialMaritalStatus)
            {
                $state = $this->CustodialMaritalStatusRepositoryInterface->destroy($CustodialMaritalStatus);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}