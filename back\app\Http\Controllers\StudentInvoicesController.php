<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentInvoicesRequest;
use App\Http\Requests\UpdateStudentInvoicesRequest;
use App\Models\StudentInvoices;
use App\Repositories\Interfaces\StudentInvoicesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class StudentInvoicesController extends Controller
{
    use ApiErrorResponse;

    private $StudentInvoicesRepositoryInterface;

    public function __construct(StudentInvoicesRepositoryInterface $StudentInvoicesRepositoryInterface)
    {
        $this->StudentInvoicesRepositoryInterface = $StudentInvoicesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->StudentInvoicesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentInvoicesRequest $request)
    {
        $state = $this->StudentInvoicesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($StudentInvoices)
    {
        $state = $this->StudentInvoicesRepositoryInterface->show($StudentInvoices);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentInvoicesRequest $request, StudentInvoices $StudentInvoices)
    {
        $state = $this->StudentInvoicesRepositoryInterface->update($request, $StudentInvoices);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentInvoices)
    {
        $state = $this->StudentInvoicesRepositoryInterface->destroy($StudentInvoices);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
