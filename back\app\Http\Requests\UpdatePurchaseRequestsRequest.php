<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePurchaseRequestsRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        return [
            'request_date' => 'required',
            'vender_supplier' => 'required',
            'priority' => 'required',
            'designation' => 'required',
            'organization_grade_level_id' => 'required',
            "manage_class_id" => "required",
            'product_image' => 'file|max:2048'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'request_date.required' => 'The request date is required.',
            'vender_supplier.required' => 'The vendor supplier is required.',
            'priority.required' => 'The priority is required.',
            'designation.required' => 'The designation is required.',
            'organization_grade_level_id.required' => 'The Grade Level is required.',
            'manage_class_id.required' => 'The Class is required.',
            'product_image.mimes' => 'The file must be less than 2 MB.',
        ];
    }
}
