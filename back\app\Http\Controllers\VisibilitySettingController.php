<?php

namespace App\Http\Controllers;

use App\Models\VisibilitySetting;
use Illuminate\Http\Request;

class VisibilitySettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(VisibilitySetting $visibilitySetting)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VisibilitySetting $visibilitySetting)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VisibilitySetting $visibilitySetting)
    {
        //
    }
}
