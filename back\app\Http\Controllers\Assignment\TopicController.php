<?php

namespace App\Http\Controllers\Assignment;

use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Assignment\Topic;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\Assignment\StoreTopicRequest;
use App\Http\Requests\Assignment\UpdateTopicRequest;
use App\Repositories\Interfaces\Assignment\TopicRepositoryInterface;

class TopicController extends Controller
{
    use ApiResponse;

    private $TopicRepositoryInterface;

    public function __construct(TopicRepositoryInterface $TopicRepositoryInterface)
    {
        $this->TopicRepositoryInterface = $TopicRepositoryInterface;
    }

    public function index(Request $request)
    {
        $result = $this->TopicRepositoryInterface->index($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function store(StoreTopicRequest $request)
    {
        if ($request->has('id')) {
            $topic = Topic::find($request->id);
            $result = $this->TopicRepositoryInterface->update($request, $topic);

        return $result ?
            $this->generalSuccess(config('constants.DATA_UPDATED'))
            :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
        $result = $this->TopicRepositoryInterface->store($request);
        return $result ?
            $this->generalSuccess(config('constants.DATA_INSERTED'))
            :
            $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function show(Topic $topic)
    {
        //
    }

    public function update(UpdateTopicRequest $request, Topic $topic)
    {
        $result = $this->TopicRepositoryInterface->update($request, $topic);

        return $result ?
            $this->generalSuccess(config('constants.DATA_UPDATED'))
            :
            $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function destroy(Topic $topic)
    {
        try {
            DB::beginTransaction();
            $this->TopicRepositoryInterface->destroy($topic);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_DELETED'));            
        } catch (\Exception $e) {
            return $this->generalError($e->getMessage() ?? config('constants.GENERAL_ERROR'));
        }
    }
}
