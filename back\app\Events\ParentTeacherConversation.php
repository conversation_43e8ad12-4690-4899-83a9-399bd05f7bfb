<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ParentTeacherConversation implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(
        public $class,
        public $course,
        public $teacher,
        public $student,
        public array $data
    ) {
    }

    public function broadcastWith()
    {
        Log::info($this->data);

        return $this->data;
    }

    public function broadcastAs()
    {
        return 'parent-teacher-conversation-event';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        $chennelId = $this->class.'-'.$this->course.'-'.$this->teacher.'-'.$this->student;

        return new PrivateChannel('parent-teacher-conversation-'.$chennelId);
    }
}
