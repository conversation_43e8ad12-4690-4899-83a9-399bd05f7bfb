<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageSchoolRequest;
use App\Http\Requests\UpdateManageSchoolRequest;
use App\Models\ManageSchool;
use App\Repositories\Interfaces\ManageSchoolRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ManageSchoolController extends Controller
{
    use ApiErrorResponse;

    private $ManageSchoolRepositoryInterface;

    public function __construct(ManageSchoolRepositoryInterface $ManageSchoolRepositoryInterface)
    {
        $this->ManageSchoolRepositoryInterface = $ManageSchoolRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageSchoolRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageSchoolRequest $request)
    {
        $state = $this->ManageSchoolRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageSchool)
    {
        $state = $this->ManageSchoolRepositoryInterface->show($ManageSchool);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageSchoolRequest $request, ManageSchool $ManageSchool)
    {
        $state = $this->ManageSchoolRepositoryInterface->update($request, $ManageSchool);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageSchool)
    {
        $state = $this->ManageSchoolRepositoryInterface->destroy($ManageSchool);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
