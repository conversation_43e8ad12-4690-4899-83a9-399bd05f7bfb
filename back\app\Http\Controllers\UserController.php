<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserPasswordRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\UserOrganizationRolesRequest;
use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    use ApiResponse;

    private $UserRepositoryInterface;

    public function __construct(UserRepositoryInterface $UserRepositoryInterface)
    {
        $this->UserRepositoryInterface = $UserRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $result = $this->UserRepositoryInterface->index();

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->UserRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($User)
    {
        $result = $this->UserRepositoryInterface->show($User);

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $User)
    {
        try {
            DB::beginTransaction();
            $this->UserRepositoryInterface->update($request, $User);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($User)
    {
        $state = $this->UserRepositoryInterface->destroy($User);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    /**update User Status */
    public function updateUserStatus(Request $request)
    {
        $state = $this->UserRepositoryInterface->updateUserStatus($request);

        return $state ? $this->generalSuccess(config('constants.DATA_UPDATED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * auto generate Password of user
     */
    public function autoGenerateUserPassword(Request $request)
    {
        $result = $this->UserRepositoryInterface->autoGenerateUserPassword($request);

        return $result ? $this->generalSuccess('Auto generated password sent successfully!') : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * add new user Organization
     */
    public function addUserNewOrganization(UserOrganizationRolesRequest $request)
    {
        $result = $this->UserRepositoryInterface->addUserNewOrganization($request);

        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * get User Organization Roles
     */
    public function getUserOrganizationRoles(Request $request)
    {
        $result['data'] = $this->UserRepositoryInterface->getUserOrganizationRoles($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * detach user organization roles
     */
    public function detachUserOrganizationRoles(Request $request)
    {
        $result = $this->UserRepositoryInterface->detachUserOrganizationRoles($request);

        return $result ? $this->successResponse(false, config('constants.DATA_DELETED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function updateUsersPassword(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->UserRepositoryInterface->updateUsersPassword($request);
            DB::commit();

            return $this->generalSuccess('Password resend successfully for selected users');
        } catch (\Exception $exp) {
            DB::rollBack();
            Log::info($exp);
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function updateUserPasswordByAdmin(UpdateUserPasswordRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->UserRepositoryInterface->updateUserPasswordByAdmin($request);
            DB::commit();

            return $this->generalSuccess('Password updated successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    // update current user password
    public function updateCurrentUserPassword(Request $request)
    {
        $data = $request->all();
        if (isset($data['current_password']) && (!Hash::check($data['current_password'], User::find($data['id'])->password))) {
            return $this->generalError('Incorrect current password!');
        }

        $result = $this->UserRepositoryInterface->updateCurrentUserPassword($request);

        return $result ? $this->generalSuccess('Password updated successfully!') : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function updateCurrentUserPin(Request $request)
    {
        $result = $this->UserRepositoryInterface->updateCurrentUserPin($request);

        return $result ? $this->generalSuccess('Pin updated successfully!') : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function tagMachineToUser(Request $request)
    {
        $data = $request->all();
        $result = $this->UserRepositoryInterface->tagMachineToUser($request);

        return $result ? $this->generalSuccess("Machine's tagged successfully!") : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /** userProfilePicture */
    public function userProfilePicture(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->UserRepositoryInterface->userProfilePicture($request);
            DB::commit();

            return $this->successResponse($result, 'Profile updated successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /** removeProfilePicture */
    public function removeProfilePicture(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->UserRepositoryInterface->removeProfilePicture($request);
            DB::commit();

            return $this->successResponse($result, 'Profile removed successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function userSignature(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->UserRepositoryInterface->userSignature($request);
            DB::commit();
            return $this->successResponse($result, 'Signature updated successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function userLoginHistory(Request $request)
    {
        $result = $this->UserRepositoryInterface->userLoginHistory($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function userNotifications(Request $request){
        $result = $this->UserRepositoryInterface->userNotifications($request);

        return response()->json($result, Response::HTTP_OK);
    }
}
