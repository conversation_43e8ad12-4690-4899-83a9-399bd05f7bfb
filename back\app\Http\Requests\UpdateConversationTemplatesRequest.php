<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateConversationTemplatesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'template_name' => [
                'required',
                'max:255',
                $Id ? Rule::unique('conversation_templates')->ignore($Id) : 'unique:conversation_templates',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'template_name.required' => 'The conversation templates name is required.',
            'template_name.max' => 'The conversation templates name must not exceed 255 characters.',
            'template_name.unique' => 'The conversation templates name is already taken. Please choose a different name.',
        ];
    }
}
