<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRecurringChargesRequest;
use App\Http\Requests\UpdateRecurringChargesRequest;
use App\Models\RecurringCharge;
use App\Models\SchoolYear;
use App\Repositories\Interfaces\RecurringChargesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Jobs\UpdateRecurringCharges;
use App\Models\EnrollmentStatus;
use App\Models\Student;
use Illuminate\Http\Request;


class RecurringChargesController extends Controller
{
    use ApiErrorResponse;

    private $RecurringChargesRepositoryInterface;

    public function __construct(RecurringChargesRepositoryInterface $RecurringChargesRepositoryInterface)
    {
        $this->RecurringChargesRepositoryInterface = $RecurringChargesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->RecurringChargesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRecurringChargesRequest $request)
    {
        $state = $this->RecurringChargesRepositoryInterface->store($request);
        return $state['status'] ? $this->successResponse($state['data'], $state['message']) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($RecurringCharges)
    {
        $state = $this->RecurringChargesRepositoryInterface->show($RecurringCharges);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreRecurringChargesRequest $request, RecurringCharge $RecurringCharges)
    {
        $state = $this->RecurringChargesRepositoryInterface->store($request);
        return $state['status'] ? $this->successResponse($state['data'], $state['message']) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($RecurringCharges)
    {
        $state = $this->RecurringChargesRepositoryInterface->destroy($RecurringCharges);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function getGradeLevelStudents()
    {
        $state = $this->RecurringChargesRepositoryInterface->getGradeLevelStudents();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getChangeInstallmentDates()
    {
        $state = $this->RecurringChargesRepositoryInterface->getChangeInstallmentDates();

        return response()->json($state, Response::HTTP_OK);
    }

    public function applySiblingDiscountAction()
    {
        $state = $this->RecurringChargesRepositoryInterface->applySiblingDiscountAction();

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DISCOUNT_APPLIED')) : '';
    }

    public function updateRecurringChargeTable()
    {
        try {
            DB::beginTransaction();

            $schoolYear = SchoolYear::where('is_default', 1)->firstOrFail();
            $schoolYear->update([
                'invoice_month_start' => $schoolYear->start_date,
                'invoice_month_end' => $schoolYear->end_date
            ]);

            $startDate = Carbon::parse($schoolYear->invoice_month_start)->startOfMonth()->toDateString();
            $endDate = Carbon::parse($schoolYear->invoice_month_end)->endOfMonth()->toDateString();

            RecurringCharge::query()->update([
                'school_year_id' => $schoolYear->id,
            ]);

            $charges = RecurringCharge::with(['charge_rate.student_charge', 'charge_rate.charge_type', 'student.enrollments' => function ($query) use ($schoolYear) {
                $query->where('school_year_id', $schoolYear->id);
                $query->whereHas('enrollmentStatus', function ($query) {
                    $query->where('type', EnrollmentStatus::TYPE_ACTIVE);
                });
            }])->get()->toArray();

            $job = new UpdateRecurringCharges($charges, $startDate, $endDate, $schoolYear->id);
            $job->handle();

            // sleep(5);

            DB::table('recurring_charges')
                ->where('charge_date', 'like', '%0000-00-00%')
                ->delete();

            // sleep(5);

            DB::table('recurring_charges')
                ->where('charge_date', 'NOT LIKE', '%2025-06-01%')
                ->update([
                    'charge_status' => 'Applied'
                ]);

            // ->chunk(500, function ($charges) use ($startDate, $endDate, $schoolYear) {
            //     $chunkData = $charges->map(fn($c) => $c->toArray())->toArray(); // Serialize models
            //     // dispatch(new UpdateRecurringCharges($chunkData, $startDate, $endDate));
            // });

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Recurring charge chunked jobs dispatched successfully.',
            ]);
        } catch (\Exception $exp) {
            // Log the exception
            Log::error($exp);
            // Rollback the transaction
            DB::rollBack();

            return ['status' => false, 'roleBack' => 'Recurring charge update failed.'];
        }
    }

    public function bulkDeleteRecurringCharges(Request $RecurringCharges)
    {
        $state = $this->RecurringChargesRepositoryInterface->bulkDeleteRecurringCharges($RecurringCharges->all());
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function bulkEditRecurringCharges(Request $request)
    {
        $state = $this->RecurringChargesRepositoryInterface->bulkEditRecurringCharges($request);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    public function tagSiblingGuardian(Request $request)
    {
        $students = Student::with(['siblings'])->get();

        // Create output array
        $result = [];

        foreach ($students as $student) {
            foreach ($student->siblings as $sibling) {
                $result[] = $sibling->pivot->toArray();
                $siblingData = $sibling->pivot->toArray();
                $guardians = Student::with(['guardians'])
                    ->where('id', $sibling->id)->first();
                if ($guardians && $guardians->guardians->isNotEmpty()) {
                    foreach ($guardians->guardians as $guardian) {
                        $alreadyLinked = DB::table('guardian_student')
                            ->where('guardian_id', $guardian->id)
                            ->where('student_id', $siblingData['sibling_id'])
                            ->exists();

                        if (!$alreadyLinked) {
                            $guardian->students()->attach($siblingData['sibling_id'], [
                                'relationship_id' => $guardian->pivot->relationship_id,
                                'is_emergency_contact' => $guardian->pivot->is_emergency_contact,
                                'is_authorized_to_pickup' => $guardian->pivot->is_authorized_to_pickup,
                                'allow_portal_access' => $guardian->pivot->allow_portal_access,
                                'invoice_and_payment_notification' => $guardian->pivot->invoice_and_payment_notification,
                                'primary_contact' => $guardian->pivot->primary_contact,
                            ]);
                        }

                        $result[] = [
                            'sibling_id' => $siblingData['sibling_id'],
                            'guardian_id' => $guardian->id,
                            'guardian_name' => $guardian->full_name,
                            'action' => 'guardian_assigned_to_sibling',
                        ];
                    }
                }
            }
        }

        return response()->json($result);
    }
}
