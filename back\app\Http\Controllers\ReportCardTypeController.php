<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\ReportCardType;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreReportCardTypeRequest;
use App\Http\Requests\UpdateReportCardTypeRequest;
use App\Repositories\Interfaces\ReportCardTypeRepositoryInterface;

class ReportCardTypeController extends Controller
{
    use ApiErrorResponse;

    private $ReportCardTypeRepositoryInterface;
    public function __construct(ReportCardTypeRepositoryInterface $ReportCardTypeRepositoryInterface)
    {
        $this->ReportCardTypeRepositoryInterface = $ReportCardTypeRepositoryInterface;
    }
        
    public function index()
    {
        $state = $this->ReportCardTypeRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }
    public function store(StoreReportCardTypeRequest $request)
    {   
        try {
            DB::beginTransaction();  
            $state = $this->ReportCardTypeRepositoryInterface->store($request);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function show($ReportCardType)
    {
        $state = $this->ReportCardTypeRepositoryInterface->show($ReportCardType);
        return response()->json($state, Response::HTTP_OK);
    }

    public function update(UpdateReportCardTypeRequest $request, ReportCardType $ReportCardType)
    {
        try {
            DB::beginTransaction();
            $state = $this->ReportCardTypeRepositoryInterface->update($request, $ReportCardType);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function destroy($ReportCardType)
    {
        $state = $this->ReportCardTypeRepositoryInterface->destroy($ReportCardType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
            
}