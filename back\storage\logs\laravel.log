[2025-07-30 12:10:16] local.ERROR: PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leave_allocation_id' in 'field list' in C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php:39
Stack trace:
#0 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(39): PDO->prepare()
#1 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\MySqlConnection->Illuminate\Database\{closure}()
#2 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#3 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(34): Illuminate\Database\Connection->run()
#4 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Processors\MySqlProcessor.php(35): Illuminate\Database\MySqlConnection->insert()
#5 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3549): Illuminate\Database\Query\Processors\MySqlProcessor->processInsertGetId()
#6 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1982): Illuminate\Database\Query\Builder->insertGetId()
#7 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1334): Illuminate\Database\Eloquent\Builder->__call()
#8 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1299): Illuminate\Database\Eloquent\Model->insertAndSetId()
#9 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1138): Illuminate\Database\Eloquent\Model->performInsert()
#10 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1025): Illuminate\Database\Eloquent\Model->save()
#11 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\helpers.php(320): Illuminate\Database\Eloquent\Builder->Illuminate\Database\Eloquent\{closure}()
#12 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1024): tap()
#13 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->create()
#14 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->forwardCallTo()
#15 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call()
#16 C:\xampp\htdocs\nexium\back\app\Repositories\LeaveAllocationRepository.php(158): Illuminate\Database\Eloquent\Model::__callStatic()
#17 C:\xampp\htdocs\nexium\back\app\Http\Controllers\LeaveAllocationController.php(44): App\Repositories\LeaveAllocationRepository->store()
#18 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\LeaveAllocationController->store()
#19 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#20 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch()
#21 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#22 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#23 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#24 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#26 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(159): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#27 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(125): Illuminate\Routing\Middleware\ThrottleRequests->handleRequest()
#28 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle()
#30 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#32 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#33 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then()
#34 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#35 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute()
#36 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#37 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch()
#38 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#39 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#40 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#41 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#42 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#43 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#44 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#45 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#46 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#47 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#48 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#49 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#50 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#51 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#52 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#53 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#54 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#55 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#56 C:\xampp\htdocs\nexium\back\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle()
#57 C:\xampp\htdocs\nexium\back\index.php(3): require_once('...')
#58 {main}

Next Illuminate\Database\QueryException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leave_allocation_id' in 'field list' (Connection: mysql, SQL: insert into `leave_type_designations` (`leave_allocation_id`, `designation_id`, `employment_length`, `updated_at`, `created_at`) values (11, 2, ?, 2025-07-30 12:10:16, 2025-07-30 12:10:16)) in C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#1 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(34): Illuminate\Database\Connection->run()
#2 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Processors\MySqlProcessor.php(35): Illuminate\Database\MySqlConnection->insert()
#3 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3549): Illuminate\Database\Query\Processors\MySqlProcessor->processInsertGetId()
#4 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1982): Illuminate\Database\Query\Builder->insertGetId()
#5 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1334): Illuminate\Database\Eloquent\Builder->__call()
#6 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1299): Illuminate\Database\Eloquent\Model->insertAndSetId()
#7 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1138): Illuminate\Database\Eloquent\Model->performInsert()
#8 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1025): Illuminate\Database\Eloquent\Model->save()
#9 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\helpers.php(320): Illuminate\Database\Eloquent\Builder->Illuminate\Database\Eloquent\{closure}()
#10 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1024): tap()
#11 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->create()
#12 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->forwardCallTo()
#13 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call()
#14 C:\xampp\htdocs\nexium\back\app\Repositories\LeaveAllocationRepository.php(158): Illuminate\Database\Eloquent\Model::__callStatic()
#15 C:\xampp\htdocs\nexium\back\app\Http\Controllers\LeaveAllocationController.php(44): App\Repositories\LeaveAllocationRepository->store()
#16 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\LeaveAllocationController->store()
#17 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#18 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch()
#19 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#20 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#21 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#22 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#23 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#24 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(159): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(125): Illuminate\Routing\Middleware\ThrottleRequests->handleRequest()
#26 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter()
#27 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle()
#28 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#29 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#30 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then()
#32 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#33 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute()
#34 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#35 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch()
#36 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#37 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#38 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#39 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#40 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#41 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#42 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#43 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#44 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#45 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#46 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#47 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#48 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#49 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#50 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#51 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#52 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#53 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#54 C:\xampp\htdocs\nexium\back\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle()
#55 C:\xampp\htdocs\nexium\back\index.php(3): require_once('...')
#56 {main}  
[2025-07-30 12:10:16] local.ERROR: PDOException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leave_allocation_id' in 'field list' in C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php:39
Stack trace:
#0 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(39): PDO->prepare()
#1 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(816): Illuminate\Database\MySqlConnection->Illuminate\Database\{closure}()
#2 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#3 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(34): Illuminate\Database\Connection->run()
#4 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Processors\MySqlProcessor.php(35): Illuminate\Database\MySqlConnection->insert()
#5 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3549): Illuminate\Database\Query\Processors\MySqlProcessor->processInsertGetId()
#6 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1982): Illuminate\Database\Query\Builder->insertGetId()
#7 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1334): Illuminate\Database\Eloquent\Builder->__call()
#8 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1299): Illuminate\Database\Eloquent\Model->insertAndSetId()
#9 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1138): Illuminate\Database\Eloquent\Model->performInsert()
#10 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1025): Illuminate\Database\Eloquent\Model->save()
#11 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\helpers.php(320): Illuminate\Database\Eloquent\Builder->Illuminate\Database\Eloquent\{closure}()
#12 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1024): tap()
#13 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->create()
#14 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->forwardCallTo()
#15 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call()
#16 C:\xampp\htdocs\nexium\back\app\Repositories\LeaveAllocationRepository.php(158): Illuminate\Database\Eloquent\Model::__callStatic()
#17 C:\xampp\htdocs\nexium\back\app\Http\Controllers\LeaveAllocationController.php(44): App\Repositories\LeaveAllocationRepository->store()
#18 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\LeaveAllocationController->store()
#19 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#20 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch()
#21 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#22 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#23 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#24 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#26 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(159): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#27 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(125): Illuminate\Routing\Middleware\ThrottleRequests->handleRequest()
#28 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter()
#29 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle()
#30 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#32 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#33 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then()
#34 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#35 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute()
#36 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#37 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch()
#38 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#39 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#40 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#41 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#42 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#43 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#44 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#45 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#46 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#47 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#48 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#49 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#50 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#51 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#52 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#53 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#54 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#55 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#56 C:\xampp\htdocs\nexium\back\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle()
#57 C:\xampp\htdocs\nexium\back\index.php(3): require_once('...')
#58 {main}

Next Illuminate\Database\QueryException: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'leave_allocation_id' in 'field list' (Connection: mysql, SQL: insert into `leave_type_designations` (`leave_allocation_id`, `designation_id`, `employment_length`, `updated_at`, `created_at`) values (11, 1, ?, 2025-07-30 12:10:16, 2025-07-30 12:10:16)) in C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php:829
Stack trace:
#0 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Connection.php(783): Illuminate\Database\Connection->runQueryCallback()
#1 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\MySqlConnection.php(34): Illuminate\Database\Connection->run()
#2 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Processors\MySqlProcessor.php(35): Illuminate\Database\MySqlConnection->insert()
#3 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Query\Builder.php(3549): Illuminate\Database\Query\Processors\MySqlProcessor->processInsertGetId()
#4 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1982): Illuminate\Database\Query\Builder->insertGetId()
#5 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1334): Illuminate\Database\Eloquent\Builder->__call()
#6 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1299): Illuminate\Database\Eloquent\Model->insertAndSetId()
#7 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(1138): Illuminate\Database\Eloquent\Model->performInsert()
#8 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1025): Illuminate\Database\Eloquent\Model->save()
#9 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\helpers.php(320): Illuminate\Database\Eloquent\Builder->Illuminate\Database\Eloquent\{closure}()
#10 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Builder.php(1024): tap()
#11 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Support\Traits\ForwardsCalls.php(23): Illuminate\Database\Eloquent\Builder->create()
#12 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2335): Illuminate\Database\Eloquent\Model->forwardCallTo()
#13 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Database\Eloquent\Model.php(2347): Illuminate\Database\Eloquent\Model->__call()
#14 C:\xampp\htdocs\nexium\back\app\Repositories\LeaveAllocationRepository.php(158): Illuminate\Database\Eloquent\Model::__callStatic()
#15 C:\xampp\htdocs\nexium\back\app\Http\Controllers\LeaveAllocationController.php(44): App\Repositories\LeaveAllocationRepository->store()
#16 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\LeaveAllocationController->store()
#17 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(43): Illuminate\Routing\Controller->callAction()
#18 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(259): Illuminate\Routing\ControllerDispatcher->dispatch()
#19 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#20 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(806): Illuminate\Routing\Route->run()
#21 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Routing\Router->Illuminate\Routing\{closure}()
#22 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#23 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\SubstituteBindings->handle()
#24 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(159): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#25 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(125): Illuminate\Routing\Middleware\ThrottleRequests->handleRequest()
#26 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Middleware\ThrottleRequests.php(87): Illuminate\Routing\Middleware\ThrottleRequests->handleRequestUsingNamedLimiter()
#27 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Routing\Middleware\ThrottleRequests->handle()
#28 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(57): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#29 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Auth\Middleware\Authenticate->handle()
#30 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#31 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(805): Illuminate\Pipeline\Pipeline->then()
#32 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(784): Illuminate\Routing\Router->runRouteWithinStack()
#33 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(748): Illuminate\Routing\Router->runRoute()
#34 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Routing\Router.php(737): Illuminate\Routing\Router->dispatchToRoute()
#35 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(200): Illuminate\Routing\Router->dispatch()
#36 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(144): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}()
#37 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#38 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#39 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle()
#40 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#41 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle()
#42 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\TrimStrings->handle()
#43 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#44 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle()
#45 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(99): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#46 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle()
#47 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\HandleCors.php(62): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#48 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\HandleCors->handle()
#49 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#50 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(183): Illuminate\Http\Middleware\TrustProxies->handle()
#51 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(119): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}()
#52 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(175): Illuminate\Pipeline\Pipeline->then()
#53 C:\xampp\htdocs\nexium\back\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(144): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter()
#54 C:\xampp\htdocs\nexium\back\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle()
#55 C:\xampp\htdocs\nexium\back\index.php(3): require_once('...')
#56 {main}  
