<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateChargesCategoriesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'charge_category' => [
                'required',
                'max:255',
                $Id ? Rule::unique('charge_categories')->ignore($Id) : 'unique:charge_categories',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'charge_category.required' => 'The charges categories name is required.',
            'charge_category.max' => 'The charges categories name must not exceed 255 characters.',
            'charge_category.unique' => 'The charges categories name is already taken. Please choose a different name.',
        ];
    }
}
