<?php

require_once 'vendor/autoload.php';

use App\Models\EmployeeLeave;
use App\Models\LeaveAllocationDesignation;

// Test the model table configuration
echo "Testing LeaveAllocationDesignation model...\n";

$model = new LeaveAllocationDesignation();
echo "Table name: " . $model->getTable() . "\n";
echo "Fillable fields: " . implode(', ', $model->getFillable()) . "\n";

// Test if we can create a simple instance (without actually saving)
try {
    $testData = [
        'designation_id' => 1,
        'leave_allocation_id' => 1,
        'employment_length' => 12
    ];
    
    $instance = new LeaveAllocationDesignation($testData);
    echo "Model instance created successfully with test data\n";
    echo "Test data: " . json_encode($testData) . "\n";
} catch (Exception $e) {
    echo "Error creating model instance: " . $e->getMessage() . "\n";
}

echo "\nTesting EmployeeLeave model...\n";
$employeeLeave = new EmployeeLeave();
echo "Table name: " . $employeeLeave->getTable() . "\n";
echo "Fillable fields: " . implode(', ', $employeeLeave->getFillable()) . "\n";

echo "\nTest completed.\n";
