<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageBusesRequest;
use App\Http\Requests\UpdateManageBusesRequest;
use App\Models\ManageBuses;
use App\Repositories\Interfaces\ManageBusesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ManageBusesController extends Controller
{
    use ApiErrorResponse;

    private $ManageBusesRepositoryInterface;

    public function __construct(ManageBusesRepositoryInterface $ManageBusesRepositoryInterface)
    {
        $this->ManageBusesRepositoryInterface = $ManageBusesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageBusesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageBusesRequest $request)
    {
        $state = $this->ManageBusesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageBuses)
    {
        $state = $this->ManageBusesRepositoryInterface->show($ManageBuses);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageBusesRequest $request, ManageBuses $ManageBuses)
    {
        $state = $this->ManageBusesRepositoryInterface->update($request, $ManageBuses);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageBuses)
    {
        $state = $this->ManageBusesRepositoryInterface->destroy($ManageBuses);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
