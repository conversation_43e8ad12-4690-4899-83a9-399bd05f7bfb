<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UpdateChargesTypesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->get('id');
        $data = [
            'charge_type' => [
                'required',
                'max:255',
                $id ? Rule::unique('charge_types')->ignore($id) : 'unique:charge_types',
            ],
            'charge_category_id' => 'required',
        ];

        // Add a custom validation rule for recurring_interval
        Validator::extend('required_if_recurring', function ($attribute, $value, $parameters, $validator) {
            $isRecurring = $validator->getData()['is_recurring'] ?? false;

            return $isRecurring ? ! empty($value) : true;
        });

        if ($this->get('is_recurring')) {
            $data['recurring_interval'] = 'required_if_recurring';
        }

        return $data;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'charge_type.required' => 'The charges types name is required.',
            'charge_type.max' => 'The charges types name must not exceed 255 characters.',
            'charge_type.unique' => 'The charges types name is already taken. Please choose a different name.',
            'charge_category_id.required' => 'The charge category is required.',
            'recurring_interval.required_if_recurring' => 'The recurring interval is required when is_recurring is true.',
        ];
    }
}
