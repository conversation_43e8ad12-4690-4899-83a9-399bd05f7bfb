<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UpdateSchoolTermRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        $schoolYearId = $this->get('school_year_id');
        $type = $this->get('type');

        return [
            'term_name' => [
                'required',
                'max:255',
                $Id
                    ? Rule::unique('school_terms')->where(function ($query) use ($schoolYearId, $Id, $type) {
                        return $query->where('school_year_id', $schoolYearId)
                            ->where('type', $type)
                            ->where('id', '<>', $Id);
                    })
                    : Rule::unique('school_terms')->where(function ($query) use ($schoolYearId, $type) {
                        return $query->where('school_year_id', $schoolYearId)
                            ->where('type', $type);
                    }),
            ],
            'term_start_end' => [
                'required',
                function ($attribute, $value, $fail) use ($Id, $type, $schoolYearId) {
                    // Parse the date range from the input value
                    [$start, $end] = $value;

                    $query = DB::table('school_terms')
                        ->where('school_year_id', $schoolYearId)
                        ->where('type', $type);

                    if ($Id) {
                        // Exclude the current record when updating
                        $query->where('id', '<>', $Id);
                    }

                    // Check if the date range overlaps with existing records
                    $conflict = $query->where(function ($q) use ($start, $end) {
                        $q->whereBetween('start_date', [$start, $end])
                            ->orWhereBetween('end_date', [$start, $end])
                            ->orWhere(function ($q) use ($start, $end) {
                                $q->where('start_date', '<=', $start)
                                    ->where('end_date', '>=', $end);
                            });
                    })->exists();

                    if ($conflict) {
                        $fail('The selected date range must be unique. Please choose a different range.');
                    }
                },
            ],
            'marks_term' => [
                'required',
                function ($attribute, $value, $fail) use ($schoolYearId, $type, $Id) {
                    $exists = DB::table('school_terms')
                        ->where('school_year_id', $schoolYearId)
                        ->where('type', $type)
                        ->where('marks_term', $value)
                        ->when($Id, fn($query) => $query->where('id', '!=', $Id))
                        ->exists();

                    if ($exists) {
                        $fail('The selected marks term already exists for the given type and school year.');
                    }
                },
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'term_name.required' => 'The school term name is required.',
            'term_name.max' => 'The school term name must not exceed 255 characters.',
            'term_name.unique' => 'The school term name is already taken for this school year. Please choose a different name.',
            'term_start_end.required' => 'The school term start and end dates are required.',
        ];
    }
}
