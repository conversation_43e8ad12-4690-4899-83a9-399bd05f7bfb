<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StudentLeaves;
use App\Http\Requests\StoreStudentLeavesRequest;
use App\Http\Requests\UpdateStudentLeavesRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\StudentLeavesRepositoryInterface;
use App\Traits\ApiErrorResponse;

class StudentLeavesController extends Controller
{
    use ApiErrorResponse;

    private $StudentLeavesRepositoryInterface;

    
            public function __construct(StudentLeavesRepositoryInterface $StudentLeavesRepositoryInterface)
            {
                $this->StudentLeavesRepositoryInterface = $StudentLeavesRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->StudentLeavesRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreStudentLeavesRequest $request)
            {
                $state = $this->StudentLeavesRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($StudentLeaves)
            {
                $state = $this->StudentLeavesRepositoryInterface->show($StudentLeaves);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateStudentLeavesRequest $request, StudentLeaves $StudentLeaves)
            {
                $state = $this->StudentLeavesRepositoryInterface->update($request, $StudentLeaves);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($StudentLeaves)
            {
                $state = $this->StudentLeavesRepositoryInterface->destroy($StudentLeaves);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }

            public function getPendingInvoicesRange(Request $request)
            {
                $state = $this->StudentLeavesRepositoryInterface->getPendingInvoicesRange($request);
                return response()->json($state, Response::HTTP_OK);
            }

            public function sendPendingInvoices(Request $request){
                $state = $this->StudentLeavesRepositoryInterface->sendPendingInvoices($request);
                return $state ? $this->successResponse(false, config('constants.EMAIL_SENT')) : '';
            }
            
}