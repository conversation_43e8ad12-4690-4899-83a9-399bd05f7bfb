<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateReportCardBatchRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'batch_name' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('report_card_batches')->ignore($Id) : 'unique:report_card_batches',
                    ],
                    'batch_date' => 'required|date',
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'batch_name.required' => 'The report card batch name is required.',
                    'batch_name.max' => 'The report card batch name must not exceed 255 characters.',
                    'batch_name.unique' => 'The report card batch name is already taken. Please choose a different name.',
                    'batch_date.required' => 'The report card batch date is required.',
                ];
            }
        
}