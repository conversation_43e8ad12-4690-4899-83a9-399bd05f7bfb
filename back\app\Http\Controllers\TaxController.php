<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Tax;
use App\Http\Requests\StoreTaxRequest;
use App\Http\Requests\UpdateTaxRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\TaxRepositoryInterface;
use App\Traits\ApiErrorResponse;

class TaxController extends Controller
{
    use ApiErrorResponse;

    private $TaxRepositoryInterface;

    
            public function __construct(TaxRepositoryInterface $TaxRepositoryInterface)
            {
                $this->TaxRepositoryInterface = $TaxRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->TaxRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreTaxRequest $request)
            {
                $state = $this->TaxRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($Tax)
            {
                $state = $this->TaxRepositoryInterface->show($Tax);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateTaxRequest $request, Tax $Tax)
            {
                $state = $this->TaxRepositoryInterface->update($request, $Tax);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($Tax)
            {
                $state = $this->TaxRepositoryInterface->destroy($Tax);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}