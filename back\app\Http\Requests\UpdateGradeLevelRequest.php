<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateGradeLevelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->get('id');
        $type = $this->get('type');
        $organizationId = Auth::user()->organization_id;

        return [
            'grade_level' => [
                'required',
                'max:255',
                // Additional check for organization_id and grade_level combination
                function ($attribute, $value, $fail) use ($organizationId, $id, $type) {
                    if ($organizationId) {
                        $exists = DB::table('organization_grade_level')
                            ->join('grade_levels', 'organization_grade_level.grade_level_id', '=', 'grade_levels.id')
                            ->where('organization_grade_level.organization_id', $organizationId)
                            ->where('grade_levels.grade_level', $value)
                            ->where('grade_levels.type', $type)
                            ->where('grade_levels.id', '!=', $id ?: null)
                            ->exists();
                        if ($exists) {
                            $fail('The grade level name is already taken. Please choose a different name.');
                        }
                    }
                },
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'grade_level.required' => 'The grade level name is required.',
            'grade_level.max' => 'The grade level name must not exceed 255 characters.',
            'grade_level.unique' => 'The grade level name is already taken. Please choose a different name.',
        ];
    }
}
