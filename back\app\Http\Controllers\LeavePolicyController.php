<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LeavePolicy;
use App\Http\Requests\StoreLeavePolicyRequest;
use App\Http\Requests\UpdateLeavePolicyRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\LeavePolicyRepositoryInterface;
use App\Traits\ApiErrorResponse;

class LeavePolicyController extends Controller
{
    use ApiErrorResponse;

    private $LeavePolicyRepositoryInterface;

    
            public function __construct(LeavePolicyRepositoryInterface $LeavePolicyRepositoryInterface)
            {
                $this->LeavePolicyRepositoryInterface = $LeavePolicyRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->LeavePolicyRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreLeavePolicyRequest $request)
            {
                $state = $this->LeavePolicyRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($LeavePolicy)
            {
                $state = $this->LeavePolicyRepositoryInterface->show($LeavePolicy);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateLeavePolicyRequest $request, LeavePolicy $LeavePolicy)
            {
                $state = $this->LeavePolicyRepositoryInterface->update($request, $LeavePolicy);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($LeavePolicy)
            {
                $state = $this->LeavePolicyRepositoryInterface->destroy($LeavePolicy);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}