<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreConversationTypesRequest;
use App\Http\Requests\UpdateConversationTypesRequest;
use App\Models\ConversationTypes;
use App\Repositories\Interfaces\ConversationTypesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ConversationTypesController extends Controller
{
    use ApiErrorResponse;

    private $ConversationTypesRepositoryInterface;

    public function __construct(ConversationTypesRepositoryInterface $ConversationTypesRepositoryInterface)
    {
        $this->ConversationTypesRepositoryInterface = $ConversationTypesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ConversationTypesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreConversationTypesRequest $request)
    {
        $state = $this->ConversationTypesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ConversationTypes)
    {
        $state = $this->ConversationTypesRepositoryInterface->show($ConversationTypes);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateConversationTypesRequest $request, ConversationTypes $ConversationTypes)
    {
        $state = $this->ConversationTypesRepositoryInterface->update($request, $ConversationTypes);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ConversationTypes)
    {
        $state = $this->ConversationTypesRepositoryInterface->destroy($ConversationTypes);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
