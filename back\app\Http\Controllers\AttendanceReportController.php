<?php

namespace App\Http\Controllers;

use App\Repositories\Interfaces\AttendanceReportRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AttendanceReportController extends Controller
{
    use ApiErrorResponse;

    private $AttendanceReportRepositoryInterface;

    public function __construct(AttendanceReportRepositoryInterface $AttendanceReportRepositoryInterface)
    {
        $this->AttendanceReportRepositoryInterface = $AttendanceReportRepositoryInterface;
    }

    /**get attendance codes */
    public function getAttendanceCodes(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getAttendanceCodes($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getAttendanceByStatus(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getAttendanceByStatus($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getStudentAttReport(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getStudentAttReport($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getAttendanceSummary(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getAttendanceSummary($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getClassStudents(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getClassStudents($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getAttendanceDashboardReport(Request $request)
    {
        $result['data'] = $this->AttendanceReportRepositoryInterface->getAttendanceDashboardReport($request);
        return response()->json($result, Response::HTTP_OK);
    }
}
