<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreEnrollmentStatusRequest;
use App\Http\Requests\UpdateEnrollmentStatusRequest;
use App\Models\EnrollmentStatus;
use App\Repositories\Interfaces\EnrollmentStatusRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class EnrollmentStatusController extends Controller
{
    use ApiErrorResponse;

    private $EnrollmentStatusRepositoryInterface;

    public function __construct(EnrollmentStatusRepositoryInterface $EnrollmentStatusRepositoryInterface)
    {
        $this->EnrollmentStatusRepositoryInterface = $EnrollmentStatusRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->EnrollmentStatusRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreEnrollmentStatusRequest $request)
    {
        $state = $this->EnrollmentStatusRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($EnrollmentStatus)
    {
        $state = $this->EnrollmentStatusRepositoryInterface->show($EnrollmentStatus);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEnrollmentStatusRequest $request, EnrollmentStatus $EnrollmentStatus)
    {
        $state = $this->EnrollmentStatusRepositoryInterface->update($request, $EnrollmentStatus);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($EnrollmentStatus)
    {
        $state = $this->EnrollmentStatusRepositoryInterface->destroy($EnrollmentStatus);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
