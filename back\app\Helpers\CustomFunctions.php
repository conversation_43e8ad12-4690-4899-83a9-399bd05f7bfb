<?php

use App\Services\EmailService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\SimpleExcel\SimpleExcelReader;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Logo;
use Carbon\Carbon;

if (!function_exists('isAdmin')) {
    function isAdmin()
    {
        $userRoles =  Auth::user()->roles->pluck('name')->unique()->toArray();
        return !empty(array_intersect($userRoles, ['Super Admin', 'Admin']));
    }
}
if (!function_exists('sendEmail')) {
    function sendEmail($to, $subject, $viewName, $emailData)
    {
        $emailService = new EmailService;
        $emailService->sendEmail($to, $subject, $viewName, $emailData);
    }
}
//File Uploading Code
if (!function_exists('fileUpload')) {
    function fileUpload($requestData, $destinationPath)
    {
        // return 'asd';
        if ($requestData->hasFile('has_attachment')) {
            $file = $requestData->file('has_attachment');

            $file_info = [
                'file_name' => $file->getClientOriginalName(),
                'file_extension' => $file->getClientOriginalExtension(),
                'file_real_path' => $file->getRealPath(),
                'file_size' => $file->getSize(),
                'file_mime_type' => $file->getMimeType(),
            ];

            // Generate a unique filename to prevent overwriting existing files
            $uniqueFileName = uniqid(16) . '.' . $file->getClientOriginalExtension();

            $fileupload = $file->move(public_path(env('AWS_BUCKET_SCHOOL') . '/' . $destinationPath), $uniqueFileName);

            if ($fileupload) {
                // Append the server path to the file_info array
                $file_info['unique_file_name'] = $uniqueFileName;

                return $file_info;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}

if (!function_exists('updateMigrationFile')) {
    function updateMigrationFile($latestMigrationFile)
    {
        // Specify the path for the service provider file
        $filePath = database_path('migrations/' . $latestMigrationFile);

        // Read the content of the service provider file
        $content = file_get_contents($filePath);

        // Define the strings to add after the line 'use Illuminate\Support\ServiceProvider;'
        $nameFieldString = '$table->string("name");' . "\n";

        // Search for the line '$table->id();'
        $searchString = '$table->id();';

        // Add the nameFieldString after the found line
        $content = str_replace($searchString, $searchString . PHP_EOL . $nameFieldString, $content);

        // Write the modified content back to the service provider file
        file_put_contents($filePath, $content, LOCK_EX);
    }
}

//
if (!function_exists('getRendomNumber')) {
    function getRendomNumber($first_name, $last_name)
    {        // Get the first letters of first and last name
        $parts = explode(' ', $first_name);
        $firstName = $parts[0];
        $parts_2 = explode(' ', $last_name);
        $lastName = $parts_2[0];

        // Generate four random digits
        $randomNumbers = [];
        for ($i = 0; $i < 4; $i++) {
            $randomNumbers[] = rand(0, 9); // Random digits from 0 to 9
        }

        // Combine the parts to create the login ID
        $loginId = $firstName . '.' . $lastName . '-' . implode('', $randomNumbers);

        return $loginId; // Output: JD-1234 (example)

    }
}

// if (!function_exists('singleFileUpload')) {
//     function singleFileUpload($destinationPath, $requestData, $fileName)
//     {
//         $file = $requestData->file($fileName);
//         if ($file) {
//             $uploadedFilePath = $file->store($destinationPath);

//             return $uploadedFilePath;
//         }

//         return false;
//     }
// }

if (!function_exists('isEnrollmentAfterChargeMonth')) {
    function isEnrollmentAfterChargeMonth($enrollmentDate, $chargeDate): bool
    {
        $enrollment = Carbon::parse($enrollmentDate)->startOfMonth();
        $charge = Carbon::parse($chargeDate)->startOfMonth();

        return $enrollment->greaterThan($charge);
    }
}

if (!function_exists('singleFileUpload')) {
    function singleFileUpload($destinationPath, $requestData, $fileName)
    {
        $file = $requestData->file($fileName);
        if ($file) {
            $publicPath = public_path($destinationPath);

            // Ensure the directory exists
            if (!file_exists($publicPath)) {
                mkdir($publicPath, 0777, true);
            }

            // Generate a unique filename
            $fileName = time() . '_' . $file->getClientOriginalName();
            $fileName = trim(Str::replace(' ', '', $fileName));
            // Move the file to the public directory
            $file->move($publicPath, $fileName);

            return "$destinationPath/$fileName";
        }

        return false;
    }
}

if (!function_exists('s3SingleFileUpload')) {
    function s3SingleFileUpload($destinationPath, $requestData, $fileName, $folderName = null, $customFileName = null)
    {
        $file = $requestData->file($fileName);
        info($file);
        if ($file) {

            if ($customFileName) {
                $fileName = $customFileName;
            } else {
                // Generate a unique file name
                $fileName = time() . '_' . $file->getClientOriginalName();
            }

            try {
                // Upload directly to S3
                $path = Storage::disk('s3')->putFileAs($destinationPath, $file, $fileName);
                return $publicUrl = $folderName  . $fileName;
            } catch (\Exception $e) {
                Log::error('S3 Upload Error: ' . $e->getMessage());
                return false;
            }
        }
        return false;
    }
}
if (!function_exists('s3MultiFileUpload')) {
    function s3MultiFileUpload($destinationPath, $requestData, $fileName, $folderName = null)
    {
        $uploadedFiles = $requestData->file($fileName);
        $uploadedFileInfo = [];

        if ($uploadedFiles && is_array($uploadedFiles)) {
            foreach ($uploadedFiles as $file) {
                $uniqueName = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();
                try {
                    Storage::disk('s3')->putFileAs($destinationPath, $file, $uniqueName);
                    $uploadedFileInfo[] = [
                        'name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                        'path' => $folderName . $uniqueName
                    ];
                } catch (\Exception $e) {
                    Log::error('S3 Upload Error: ' . $e->getMessage());
                }
            }
        } elseif ($uploadedFiles instanceof \Illuminate\Http\UploadedFile) {
            $file = $uploadedFiles;
            $uniqueName = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();
            try {
                Storage::disk('s3')->putFileAs($destinationPath, $file, $uniqueName);
                $uploadedFileInfo[] = [
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'type' => $file->getMimeType(),
                    'path' => $folderName . $uniqueName
                ];
            } catch (\Exception $e) {
                Log::error('S3 Upload Error: ' . $e->getMessage());
            }
        }
        // Filter out any null/false entries (if any) before return
        return array_values(array_filter($uploadedFileInfo));
    }
}



/**
 * upload base64 image
 */
if (!function_exists('uploadBase64Image')) {
    function uploadBase64Image($destinationPath, $fileName)
    {
        return $fileName;
        // Extract the image data and extension from the base64 string
        $extension = explode('/', explode(':', substr($fileName, 0, strpos($fileName, ';')))[1])[1];
        $replace = substr($fileName, 0, strpos($fileName, ',') + 1);
        // Find substring for replace here, e.g., data:image/png;base64,
        $image = str_replace($replace, '', $fileName);
        $image = str_replace(' ', '+', $image);
        $imageName = Str::random(10) . '.' . $extension;

        // Store the image in the specified destination path
        $filePath = $destinationPath . '/' . $imageName;
        Storage::put($filePath, base64_decode($image));

        // Return the full file path
        return str_replace('public/', '', $filePath);
    }
}

if (!function_exists('readExcelFile')) {
    function readExcelFile($requestData, $fileName)
    {
        $path = 'imports/';
        $fileInfo = singleFileUpload($path, $requestData, $fileName);
        $path = Storage::path($fileInfo);
        // Create a SimpleExcelReader instance
        $reader = SimpleExcelReader::create($path)->trimHeaderRow();

        // Get the rows from the Excel file
        return $reader->getRows();
    }
}

// Define your custom helper function
if (!function_exists('formatAmount')) {
    function formatAmount($amount, $currencySymbol = '$', $precision = 2)
    {
        // Check if the amount is negative
        if ($amount < 0) {
            $formattedAmount = number_format(abs($amount), $precision, '.', ',');
            return "({$currencySymbol}{$formattedAmount})";
        }

        // Return formatted amount normally if positive
        return $currencySymbol . number_format((float)$amount, $precision, '.', ',');
    }
}

if (!function_exists('generateRandomColor')) {
    function generateRandomColor()
    {
        $letters = '0123456789ABCDEF';
        $color = '#';

        for ($i = 0; $i < 6; $i++) {
            $color .= $letters[rand(0, 15)];
        }

        return $color;
    }
}

if (!function_exists('generateRandomBgColor')) {
    function generateRandomBgColor()
    {
        $bgColor = ['#F9C674', '#02C3A8', '#6489E1', '#FD7066'];
        $randomColor = $bgColor[array_rand($bgColor)];

        return $randomColor;
    }
}


if (!function_exists('extractAndFormatAmount')) {
    function extractAndFormatAmount($string)
    {
        // Better regex to match amounts with or without commas and decimals
        preg_match('/(\d[\d,]*\.?\d*)/', $string, $matches);

        if (isset($matches[1])) {
            // Remove commas
            $formattedAmount = str_replace(',', '', $matches[1]);
            return $formattedAmount;
        } else {
            return null; // Amount not found
        }
    }
}

if (!function_exists('activeSchoolYear')) {
    function activeSchoolYear()
    {
        return auth()->user()->schoolYear;
    }
}

if (!function_exists('userOrganizations')) {
    function userOrganizations($organization)
    {
        if ($organization == 'all') {
            return auth()->user()->organizations->pluck('id')->toArray();
        } else {
            return [$organization];
        }
    }
}

if (!function_exists('isTeacherExist')) {
    function isTeacherExist()
    {
        $teacherId = 0;
        if (isset(Auth::user()->teacherData) && Auth::user()->teacherData != null) {
            $teacherId = Auth::user()->teacherData['id'];
        }
        return $teacherId;
    }
}

if (!function_exists('getApplicationLogo')) {
    function getApplicationLogo($name)
    {
        try {
            $filePath = $name ? Logo::where('name', $name)->value('path') : null;
            return env('S3_FILE_APP_URL') . env('S3_FILE_BUCKET_FOLDER') . $filePath;
        } catch (\Exception $e) {
            return null;
        }
    }
}
