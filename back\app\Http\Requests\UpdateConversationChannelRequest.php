<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateConversationChannelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'conversation_channel' => [
                'required',
                'max:255',
                $Id ? Rule::unique('conversation_channels')->ignore($Id) : 'unique:conversation_channels',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The conversation channel name is required.',
            'name.max' => 'The conversation channel name must not exceed 255 characters.',
            'name.unique' => 'The conversation channel name is already taken. Please choose a different name.',
        ];
    }
}
