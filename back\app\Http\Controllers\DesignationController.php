<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Designation;
use App\Http\Requests\StoreDesignationRequest;
use App\Http\Requests\UpdateDesignationRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\DesignationRepositoryInterface;
use App\Traits\ApiErrorResponse;

class DesignationController extends Controller
{
    use ApiErrorResponse;

    private $DesignationRepositoryInterface;

    
            public function __construct(DesignationRepositoryInterface $DesignationRepositoryInterface)
            {
                $this->DesignationRepositoryInterface = $DesignationRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->DesignationRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreDesignationRequest $request)
            {
                $state = $this->DesignationRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($Designation)
            {
                $state = $this->DesignationRepositoryInterface->show($Designation);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateDesignationRequest $request, Designation $Designation)
            {
                $state = $this->DesignationRepositoryInterface->update($request, $Designation);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($Designation)
            {
                $state = $this->DesignationRepositoryInterface->destroy($Designation);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}