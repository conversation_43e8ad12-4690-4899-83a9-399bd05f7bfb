<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePaymentRequest;
use App\Http\Requests\UpdatePaymentRequest;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Models\InvoicePaymentStudentCharge;
use App\Models\Payment;
use App\Models\StudentCharge;
use App\Models\StudentLedger;
use App\Repositories\Interfaces\PaymentRepositoryInterface;
use App\Traits\ApiErrorResponse;
use App\Traits\PaymentTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    use ApiErrorResponse, PaymentTrait;

    private $PaymentRepositoryInterface;

    public function __construct(PaymentRepositoryInterface $PaymentRepositoryInterface)
    {
        $this->PaymentRepositoryInterface = $PaymentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->PaymentRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    public function get_all_eft_payment()
    {
        $state = $this->PaymentRepositoryInterface->get_all_eft_payment();
        return response()->json($state, Response::HTTP_OK);
    }

    public function get_student_pending_invoice(Request $request)
    {
        $state = $this->get_student_pending_invoices($request->student_id, false, false, $request->organization_id);

        return response()->json($state, Response::HTTP_OK);
    }

    public function store(StorePaymentRequest $request)
    {

        $state = $this->PaymentRepositoryInterface->store($request);
        if (isset($state['status']) && $state['status'] == false) {
            return $this->generalWarning($state['message'], 200, $state);
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function show($Payment)
    {
        $state = $this->PaymentRepositoryInterface->show($Payment);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePaymentRequest $request, Payment $Payment)
    {
        $state = $this->PaymentRepositoryInterface->update($request, $Payment);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Payment)
    {
        $state = $this->PaymentRepositoryInterface->destroy($Payment);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function import_eft_file(Request $Payment)
    {
        $state = $this->PaymentRepositoryInterface->import_eft_file($Payment);
        if ($state['status'] == false && $state['status_type'] == 1) {
            return $this->generalWarning($state['message'], 200, $state);
        } elseif ($state['status'] == false && $state['status_type'] == 2) {
            return $this->generalWarning($state['message'], 200, $state);
        } else {
            return $state['status'] ? $this->successResponse(false, config('constants.EFT_UPLOAD')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function paymentRollBack(Request $Payment)
    {
        $state = $this->PaymentRepositoryInterface->paymentRollBack($Payment);
        if (isset($state['status']) && $state['status'] == false) {
            return $this->generalWarning($state['message'], 200, $state);
        } else {
            return $state ? $this->successResponse(false, config('constants.EFT_ROLLBACK')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
