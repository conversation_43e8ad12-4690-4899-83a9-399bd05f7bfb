<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePermissionRequest;
use App\Http\Requests\UpdatePermissionRequest;
use App\Models\Permission;
use App\Repositories\Interfaces\PermissionRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class PermissionController extends Controller
{
    use ApiErrorResponse;

    /**
     * Display a listing of the resource.
     */
    private $PermissionRepositoryInterface;

    public function __construct(PermissionRepositoryInterface $PermissionRepositoryInterface)
    {
        $this->PermissionRepositoryInterface = $PermissionRepositoryInterface;
    }

    public function index()
    {
        $state = $this->PermissionRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePermissionRequest $request)
    {
        $state = $this->PermissionRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePermissionRequest $request, Permission $permission)
    {
        $state = $this->PermissionRepositoryInterface->update($request, $permission);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($permission)
    {
        $state = $this->PermissionRepositoryInterface->destroy($permission);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
