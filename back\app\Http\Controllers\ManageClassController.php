<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClassCourseRequest;
use App\Http\Requests\ClassStudentRequest;
use App\Http\Requests\CourseResourceRequest;
use App\Http\Requests\SaveErsRequest;
use App\Http\Requests\StoreManageClassRequest;
use App\Http\Requests\UpdateManageClassRequest;
use App\Models\GoogleToken;
use App\Models\ManageClass;
use App\Repositories\Interfaces\ManageClassRepositoryInterface;
use App\Traits\ApiResponse;
use App\Traits\GoogleClassroomTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;

class ManageClassController extends Controller
{
    use ApiResponse;
    use GoogleClassroomTrait;

    private $ManageClassRepositoryInterface;

    private $userToken;

    public function __construct(ManageClassRepositoryInterface $ManageClassRepositoryInterface)
    {
        $this->ManageClassRepositoryInterface = $ManageClassRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageClassRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getErs()
    {
        $state = $this->ManageClassRepositoryInterface->getErs();

        return response()->json($state, Response::HTTP_OK);
    }

    public function GoogleClassServiceUrl(Request $request)
    {
        return $this->initializeGoogleClassroomClient();
    }

    public function handleGoogleCallback(Request $request)
    {
        $client = $this->getGoogleClient();
        $token = $client->fetchAccessTokenWithAuthCode($request->code);
        $token_data = json_encode($token);

        return Redirect::to('http://localhost:3000/app/default?token=' . $token_data);
    }

    protected function storeGoogleToken(Request $request)
    {
        // Save the token in the GoogleToken model
        GoogleToken::updateOrCreate(
            ['user_id' => auth()->user()->id], // Assuming there's a foreign key 'user_id' in GoogleToken table
            [
                'token' => $request->googleToken,
                'isrevoke' => 0,
                // Add other fields you want to store
            ]
        );
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageClassRequest $request)
    {
        $state = $this->ManageClassRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function addClassCourse(ClassCourseRequest $request)
    {
        $state = $this->ManageClassRepositoryInterface->addClassCourse($request);
        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function addCourseResource(CourseResourceRequest $request)
    {
        $state = $this->ManageClassRepositoryInterface->addCourseResource($request);
        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function addClassCourseScheduleActivity(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->addClassCourseScheduleActivity($request);

        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function addClassCourseWeightage(Request $request)
    {
         $validator = Validator::make(
            $request->all(),
            [
                'class_course_id' => 'required|integer|exists:class_courses,id',
                'class_course_weightage.*.weightage' => 'required|numeric',
            ],
            [
                'class_course_id.required' => 'Class course is required.',
                'class_course_id.integer' => 'Class course ID must be an integer.',
                'class_course_id.exists' => 'The selected class course does not exist.',

                'class_course_weightage.*.weightage.required' => 'Each weightage is required.',
                'class_course_weightage.*.weightage.numeric' => 'Each weightage must be a number.',
            ]
        );

        if ($validator->fails()) {
            return $this->generalError($validator->messages()->first());
        }
        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->addClassCourseWeightage($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError($exp->getMessage());
        }
    }
    public function UpdateClassCourseWeightage(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'class_course_id' => 'required|integer|exists:class_courses,id',
                'weightage' => 'required|numeric',
            ],
            [
                'class_course_id.required' => 'Class course is required.',
                'class_course_id.integer' => 'Class course ID must be an integer.',
                'class_course_id.exists' => 'The selected class course does not exist.',

                'weightage.required' => 'Each weightage is required.',
                'weightage.numeric' => 'Each weightage must be a number.',
            ]
        );

        if ($validator->fails()) {
            return $this->generalError($validator->messages()->first());
        }

        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->UpdateClassCourseWeightage($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError($exp->getMessage());
        }
    }
    public function addClassStudent(ClassStudentRequest $request)
    {
        $state = $this->ManageClassRepositoryInterface->addClassStudent($request);

        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageClass)
    {
        $state = $this->ManageClassRepositoryInterface->show($ManageClass);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageClassRequest $request, ManageClass $ManageClass)
    {
        $state = $this->ManageClassRepositoryInterface->update($request, $ManageClass);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageClass)
    {
        $state = $this->ManageClassRepositoryInterface->destroy($ManageClass);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getOrganizationGradeLevel()
    {
        $state = $this->ManageClassRepositoryInterface->getOrganizationGradeLevel();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getTaggedClassCourseTeachers()
    {
        $state = $this->ManageClassRepositoryInterface->getTaggedClassCourseTeachers();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getOtherClasses()
    {
        $state = $this->ManageClassRepositoryInterface->getOtherClasses();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getOtherClassStudents()
    {
        $state = $this->ManageClassRepositoryInterface->getOtherClassStudents();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getGradeStudents()
    {
        $state = $this->ManageClassRepositoryInterface->getGradeStudents();

        return response()->json($state, Response::HTTP_OK);
    }

    public function deleteClassStudent()
    {
        $state = $this->ManageClassRepositoryInterface->deleteClassStudent();

        if ($state['status'] != 1 && $state['status'] == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_DELETED')) : '';
        }
    }

    public function deleteClassCourse()
    {
        $state = $this->ManageClassRepositoryInterface->deleteClassCourse();
        if ($state['status'] != 1 && $state['status'] == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_DELETED')) : '';
        }
    }

    public function deleteClassCourseTeacher()
    {
        $state = $this->ManageClassRepositoryInterface->deleteClassCourseTeacher();
        if ($state['status'] === -1) {
            return $this->generalError(config('constants.ASSIGNMENT_EXISTS'));
        }
        if ($state['status'] != 1 && $state['status'] == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_DELETED')) : '';
        }
    }

    public function deleteClassResource()
    {
        $state = $this->ManageClassRepositoryInterface->deleteClassResource();
        if ($state['status'] != 1 && $state['status'] == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_DELETED')) : '';
        }
    }

    public function classStudentFilter()
    {
        $state = $this->ManageClassRepositoryInterface->classStudentFilter();

        return response()->json($state, Response::HTTP_OK);
    }

    public function classCourseFilter()
    {
        $state = $this->ManageClassRepositoryInterface->classCourseFilter();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getClassCourseSchedules()
    {
        $state = $this->ManageClassRepositoryInterface->getClassCourseSchedules();

        return response()->json($state, Response::HTTP_OK);
    }

    public function sendEmail(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->sendEmail($request);
        if ($state === -1) {
            return $this->generalError(config('constants.MAIL_BLOCKED'));
        }

        if (isset($state['status']) && $state['status'] == false) {
            return $this->generalError($state['message']);
        } else {
            return $state ? $this->successResponse($state, config('constants.EMAIL_SENT')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function saveErs(SaveErsRequest $request)
    {
        $state = $this->ManageClassRepositoryInterface->saveErs($request);
        if (isset($state['status']) && $state['status'] == false) {
            return $this->generalError($state['message']);
        } else {
            return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function deleteErs(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->deleteErs($request);
        if (isset($state['status']) && $state['status'] == false) {
            return $this->generalError($state['message']);
        } else {
            return $state ? $this->successResponse($state, config('constants.DATA_DELETED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getGoogleClassroomStatus()
    {
        $state = $this->ManageClassRepositoryInterface->getGoogleClassroomStatus();

        return response()->json($state, Response::HTTP_OK);
    }

    public function get_class_courses(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->get_class_courses($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function get_tagged_class_resources(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->get_tagged_class_resources($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function saveLessonTopic(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->saveLessonTopic($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getClassCourseStrands(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->getClassCourseStrands($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function addClassCourseStrands(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->addClassCourseStrands($request);
            DB::commit();
            return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function updateClassCourseStrandsSort(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->updateClassCourseStrandsSort($request);
            DB::commit();
            return $this->successResponse($result, 'Sort updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function deleteClassCourseStrands(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->deleteClassCourseStrands($request);
        if ($state['status'] != 1 && $state['status'] == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_DELETED')) : '';
        }
    }
    public function updateClassCourseStrands(Request $request)
    {
        try {
            DB::beginTransaction();
            $state = $this->ManageClassRepositoryInterface->updateClassCourseStrands($request);
            DB::commit();
            return $this->successResponse($state, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getStrandsCourses(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->getStrandsCourses($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function getClassCourseWithoutStrand(Request $request)
    {
        $state = $this->ManageClassRepositoryInterface->getClassCourseWithoutStrand($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function saveClassCoursesStrands(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ManageClassRepositoryInterface->saveClassCoursesStrands($request);
            DB::commit();
            return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
