<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchoolTypeRequest;
use App\Http\Requests\UpdateSchoolTypeRequest;
use App\Models\SchoolType;
use App\Repositories\Interfaces\SchoolTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SchoolTypeController extends Controller
{
    use ApiErrorResponse;

    private $SchoolTypeRepositoryInterface;

    public function __construct(SchoolTypeRepositoryInterface $SchoolTypeRepositoryInterface)
    {
        $this->SchoolTypeRepositoryInterface = $SchoolTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SchoolTypeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolTypeRequest $request)
    {
        $state = $this->SchoolTypeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(SchoolType $SchoolType)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolTypeRequest $request, SchoolType $SchoolType)
    {
        $state = $this->SchoolTypeRepositoryInterface->update($request, $SchoolType);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SchoolType)
    {
        $state = $this->SchoolTypeRepositoryInterface->destroy($SchoolType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
