<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTimezoneCodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'timezone_code' => [
                'required',
                'max:255',
                $Id ? Rule::unique('timezone_codes')->ignore($Id) : 'unique:timezone_codes',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'timezone_code.required' => 'The timezone code name is required.',
            'timezone_code.max' => 'The timezone code name must not exceed 255 characters.',
            'timezone_code.unique' => 'The timezone code name is already taken. Please choose a different name.',
        ];
    }
}
