<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateSchoolRoomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->get('id');
        $organizationId = $this->get('organization_id') ? $this->get('organization_id') : Auth::user()->organization_id;

        return [
            'name' => [
                'required',
                'max:255',
                function ($attribute, $value, $fail) use ($organizationId, $id) {
                    if ($organizationId) {
                        $exists = DB::table('school_rooms')
                            ->where('organization_id', $organizationId)
                            ->where('name', $value)
                            ->where('id', '!=', $id)
                            ->exists();
                        if ($exists) {
                            $fail('The school room name is already taken. Please choose a different name.');
                        }
                    }
                },
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The school room name is required.',
            'name.max' => 'The school room name must not exceed 255 characters.',
            'name.unique' => 'The school room name is already taken. Please choose a different name.',
        ];
    }
}
