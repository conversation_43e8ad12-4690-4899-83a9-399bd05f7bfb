<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTemplatesTypesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'template_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('templates_types')->ignore($Id) : 'unique:templates_types',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'template_type.required' => 'The templates types name is required.',
            'template_type.max' => 'The templates types name must not exceed 255 characters.',
            'template_type.unique' => 'The templates types name is already taken. Please choose a different name.',
        ];
    }
}
