<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreChargesCategoriesRequest;
use App\Http\Requests\UpdateChargesCategoriesRequest;
use App\Models\ChargeCategory;
use App\Repositories\Interfaces\ChargesCategoriesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ChargesCategoriesController extends Controller
{
    use ApiErrorResponse;

    private $ChargesCategoriesRepositoryInterface;

    public function __construct(ChargesCategoriesRepositoryInterface $ChargesCategoriesRepositoryInterface)
    {
        $this->ChargesCategoriesRepositoryInterface = $ChargesCategoriesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ChargesCategoriesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreChargesCategoriesRequest $request)
    {
        $state = $this->ChargesCategoriesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ChargesCategories)
    {
        $state = $this->ChargesCategoriesRepositoryInterface->show($ChargesCategories);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateChargesCategoriesRequest $request, ChargeCategory $ChargesCategories)
    {
        $state = $this->ChargesCategoriesRepositoryInterface->update($request, $ChargesCategories);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ChargesCategories)
    {
        $state = $this->ChargesCategoriesRepositoryInterface->destroy($ChargesCategories);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
