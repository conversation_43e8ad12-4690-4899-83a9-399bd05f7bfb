<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageTransportRequest;
use App\Http\Requests\UpdateManageTransportRequest;
use App\Repositories\Interfaces\ManageTransportRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ManageTransportController extends Controller
{
    use ApiErrorResponse;

    private $ManageTransportRepositoryInterface;

    public function __construct(ManageTransportRepositoryInterface $ManageTransportRepositoryInterface)
    {
        $this->ManageTransportRepositoryInterface = $ManageTransportRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageTransportRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    public function get_bus_students_edit_data(Request $request)
    {
        $result = $this->ManageTransportRepositoryInterface->get_bus_students_edit_data($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageTransportRequest $request)
    {
        $state = $this->ManageTransportRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageTransport)
    {
        $state = $this->ManageTransportRepositoryInterface->show($ManageTransport);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageTransportRequest $request, $ManageTransport)
    {
        $state = $this->ManageTransportRepositoryInterface->update($request, $ManageTransport);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function delete_bus_student(Request $request)
    {
        $state = $this->ManageTransportRepositoryInterface->destroy($request);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            if (! $request->has('is_edit_bus_student')) {
                return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
            }
        }
    }

    public function get_all_route_bus_students(Request $request)
    {
        $result = $this->ManageTransportRepositoryInterface->get_all_route_bus_students($request);

        return response()->json($result, Response::HTTP_OK);
    }
}
