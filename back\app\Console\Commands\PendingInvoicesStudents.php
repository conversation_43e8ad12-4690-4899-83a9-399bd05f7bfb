<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Mail;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\{
    Organization,
    Student,
    Invoice,
    Module
};
use App\Mail\PendingInvoiceMail;
use Illuminate\Support\Facades\Log;

class PendingInvoicesStudents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'students:pending-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send invoices at email of student';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return false;
        $organization_ids = Organization::pluck('id')
            ->toArray();

        // if (isset($organization_ids) && count($organization_ids) > 0) {
        //     foreach ($organization_ids as $org_id) {
        //         $student_ids = Student::where('organization_id', $org_id)
        //             ->pluck('id')
        //             ->toArray();
        //         if (isset($student_ids) && count($student_ids) > 0) {
        //             foreach ($student_ids as $student_id) {
        //                 $invoices = Invoice::whereHas('invoice_detail.student_charge', function ($query) {
        //                     $query->where('charge_status', '!=', 'Paid');
        //                 })
        //                     ->where('student_id', $student_id)
        //                     ->where('organization_id', $org_id);

        //                 $invoices = $invoices->whereHas('invoice_batch', function ($query) {
        //                     $query->where('batch_status', 'Posted');
        //                 });

        //                 $invoices = $invoices->where(function ($query) {
        //                     $query->where('invoice_status_code', 'Pending')
        //                         ->orWhere('invoice_status_code', 'Partially Paid');
        //                 })
        //                     ->with([
        //                         'invoiceTax',
        //                         'payment.invoice',
        //                         'invoice_detail.charge_type.charge_category',
        //                         'invoice_detail.student_charge' => function ($query) {
        //                             $query->where('charge_status', '!=', 'Paid');
        //                         },
        //                         'invoice_detail.student_charge.invoice_payment',
        //                     ])
        //                     ->orderBy('id', 'asc')
        //                     ->get()
        //                     ->map(function ($invoice) use ($student_id) {
        //                         $std = Student::where('id', $student_id)
        //                             ->with(['guardians' => function ($query) {
        //                                 $query->with(['user']);
        //                             }])
        //                             ->first();


        //                         foreach ($std->guardians as $guardian) {
        //                             if (isset($guardian->user)) {
        //                                 Log::channel('failed_emails')->info($guardian);
        //                                 Mail::to($guardian->user->email)
        //                                     ->send(new PendingInvoiceMail(
        //                                         $invoice,
        //                                         $std,
        //                                         $guardian
        //                                     ));
        //                             }
        //                         }
        //                     });
        //             }
        //         }
        //     }
        // }

        return 0;
        // return Command::SUCCESS;
    }
}
