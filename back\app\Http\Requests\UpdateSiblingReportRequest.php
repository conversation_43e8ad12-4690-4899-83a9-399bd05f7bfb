<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSiblingReportRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'name' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('sibling_reports')->ignore($Id) : 'unique:sibling_reports',
                    ],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The sibling report name is required.',
                    'name.max' => 'The sibling report name must not exceed 255 characters.',
                    'name.unique' => 'The sibling report name is already taken. Please choose a different name.',
                ];
            }
        
}