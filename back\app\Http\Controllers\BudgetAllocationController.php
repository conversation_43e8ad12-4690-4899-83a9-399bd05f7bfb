<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BudgetAllocation;
use App\Http\Requests\StoreBudgetAllocationRequest;
use App\Http\Requests\UpdateBudgetAllocationRequest;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Repositories\Interfaces\BudgetAllocationRepositoryInterface;
use App\Traits\{
    ApiResponse, 
    PreLoadTrait
};

class BudgetAllocationController extends Controller
{
    use ApiResponse, PreLoadTrait;

    private $BudgetAllocationRepositoryInterface;
    public function __construct(BudgetAllocationRepositoryInterface $BudgetAllocationRepositoryInterface)
    {
        $this->BudgetAllocationRepositoryInterface = $BudgetAllocationRepositoryInterface;
    }
    public function getTeacherAllocatedBudget(Request $request){
        $result = $this->BudgetAllocationRepositoryInterface->getTeacherAllocatedBudget($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function getAllTeachersHavingUsers(Request $request){
        $result = $this->getTeachersHavingUsersTrait($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function getTeacherGradeCourses(Request $request){
        $result = $this->getTeacherGradeCoursesTrait($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function saveTeacherBudgetAllocation(Request $request){        
        try {
            DB::beginTransaction();
            $result = $this->BudgetAllocationRepositoryInterface->saveTeacherBudgetAllocation($request);
            DB::commit();
            return  $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return  $this->generalError(config('constants.GENERAL_ERROR'));
        }      
    }

    public function getAllBudgetAllocations(Request $request){
        $result = $this->BudgetAllocationRepositoryInterface->getAllBudgetAllocations($request);
        return response()->json($result, Response::HTTP_OK);
    }
   
    public function getTeacherBudget(Request $request){
        $result = $this->BudgetAllocationRepositoryInterface->getTeacherBudget($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function updateBudgetItem(Request $request){
        $result = $this->BudgetAllocationRepositoryInterface->updateBudgetItem($request);
        return response()->json($result, Response::HTTP_OK);
    }
}