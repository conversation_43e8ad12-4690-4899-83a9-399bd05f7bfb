<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDocumentStudentRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [                    
                    'student_id' => 'required',
                    'document_type_id' => 'required',
                    'original_name' => 'nullable|string',
                    'file_name' => 'nullable|string',
                    'file_path' => 'nullable|string',
                    'comments'  => 'nullable|string',
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'student_id.required' => 'The student id is required.',
                    'document_type_id.required' => 'The document type is required.',
                ];
            }
        
}