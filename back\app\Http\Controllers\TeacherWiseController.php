<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTeacherWiseRequest;
use App\Http\Requests\UpdateTeacherWiseRequest;
use App\Models\TeacherWise;
use App\Repositories\Interfaces\TeacherWiseRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TeacherWiseController extends Controller
{
    use ApiErrorResponse;

    private $TeacherWiseRepositoryInterface;

    public function __construct(TeacherWiseRepositoryInterface $TeacherWiseRepositoryInterface)
    {
        $this->TeacherWiseRepositoryInterface = $TeacherWiseRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TeacherWiseRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTeacherWiseRequest $request)
    {
        $state = $this->TeacherWiseRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($TeacherWise)
    {
        $state = $this->TeacherWiseRepositoryInterface->show($TeacherWise);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTeacherWiseRequest $request, TeacherWise $TeacherWise)
    {
        $state = $this->TeacherWiseRepositoryInterface->update($request, $TeacherWise);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TeacherWise)
    {
        $state = $this->TeacherWiseRepositoryInterface->destroy($TeacherWise);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function exportTeacherWise(Request $request)
    {
        $result = $this->TeacherWiseRepositoryInterface->exportTeacherWise($request);
        return $result;
    }
}
