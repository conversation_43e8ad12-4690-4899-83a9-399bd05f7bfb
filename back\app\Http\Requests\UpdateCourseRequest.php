<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'course' => [
                'required',
                'max:255',
                $Id ? Rule::unique('courses')->ignore($Id) : 'unique:courses',
            ],
            'course_code' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The course name is required.',
            'name.max' => 'The course name must not exceed 255 characters.',
            'name.unique' => 'The course name is already taken. Please choose a different name.',
            'course_code.required' => 'The course code is required.',
        ];
    }
}
