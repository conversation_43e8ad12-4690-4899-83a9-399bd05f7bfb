<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LeaveType;
use App\Http\Requests\StoreLeaveTypeRequest;
use App\Http\Requests\UpdateLeaveTypeRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\LeaveTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;

class LeaveTypeController extends Controller
{
    use ApiErrorResponse;

    private $LeaveTypeRepositoryInterface;

    
            public function __construct(LeaveTypeRepositoryInterface $LeaveTypeRepositoryInterface)
            {
                $this->LeaveTypeRepositoryInterface = $LeaveTypeRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->LeaveTypeRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreLeaveTypeRequest $request)
            {
                $state = $this->LeaveTypeRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.LEAVE_TYPE_CREATED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($LeaveType)
            {
                $state = $this->LeaveTypeRepositoryInterface->show($LeaveType);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateLeaveTypeRequest $request, LeaveType $LeaveType)
            {
                $state = $this->LeaveTypeRepositoryInterface->update($request, $LeaveType);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($LeaveType)
            {
                $state = $this->LeaveTypeRepositoryInterface->destroy($LeaveType);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}