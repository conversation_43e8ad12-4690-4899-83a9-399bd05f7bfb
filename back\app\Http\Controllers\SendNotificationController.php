<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSendNotificationRequest;
use App\Http\Requests\UpdateSendNotificationRequest;
use App\Models\SendNotification;
use App\Repositories\Interfaces\SendNotificationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SendNotificationController extends Controller
{
    use ApiErrorResponse;

    private $SendNotificationRepositoryInterface;

    public function __construct(SendNotificationRepositoryInterface $SendNotificationRepositoryInterface)
    {
        $this->SendNotificationRepositoryInterface = $SendNotificationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SendNotificationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSendNotificationRequest $request)
    {
        $state = $this->SendNotificationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.NOTIFICATION_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SendNotification)
    {
        $state = $this->SendNotificationRepositoryInterface->show($SendNotification);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSendNotificationRequest $request, SendNotification $SendNotification)
    {
        $state = $this->SendNotificationRepositoryInterface->update($request, $SendNotification);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SendNotification)
    {
        $state = $this->SendNotificationRepositoryInterface->destroy($SendNotification);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
