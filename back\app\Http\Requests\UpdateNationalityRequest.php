<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateNationalityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'nationality' => [
                'required',
                'max:255',
                $Id ? Rule::unique('nationalities')->ignore($Id) : 'unique:nationalities',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'nationality.required' => 'The nationality name is required.',
            'nationality.max' => 'The nationality name must not exceed 255 characters.',
            'nationality.unique' => 'The nationality name is already taken. Please choose a different name.',
        ];
    }
}
