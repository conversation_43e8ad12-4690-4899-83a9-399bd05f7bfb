<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageGuardianRequest;
use App\Http\Requests\StudentGuardianRequest;
use App\Http\Requests\UpdateManageGuardianRequest;
use App\Models\ClassStudent;
use App\Models\Guardian;
use App\Models\Student;
use App\Repositories\Interfaces\ManageGuardianRepositoryInterface;
use App\Repositories\Interfaces\StudentRepositoryInterface;
use App\Traits\AddressTrait;
use App\Traits\ApiResponse;
use App\Traits\ImportStudentTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class ManageGuardianController extends Controller
{
    use AddressTrait, ApiResponse, ImportStudentTrait;

    private $ManageGuardianRepositoryInterface;

    private $StudentRepositoryInterface;

    public function __construct(
        ManageGuardianRepositoryInterface $ManageGuardianRepositoryInterface,
        StudentRepositoryInterface $StudentRepositoryInterface
    ) {
        $this->ManageGuardianRepositoryInterface = $ManageGuardianRepositoryInterface;
        $this->StudentRepositoryInterface = $StudentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $result = $this->ManageGuardianRepositoryInterface->index();

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageGuardianRequest $request)
    {
        $result = $this->ManageGuardianRepositoryInterface->store($request);
        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageGuardian)
    {
        $state = $this->ManageGuardianRepositoryInterface->show($ManageGuardian);

        return response()->json($state, Response::HTTP_OK);
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageGuardianRequest $request, Guardian $guardian)
    {
        // $result = $this->ManageGuardianRepositoryInterface->update($request, $guardian);
        // return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    //updateGuardian
    public function updateGuardian(Request $request)
    {
        // return $request->all();
        try {
            DB::beginTransaction();
            $guardian = Guardian::find($request->id);
            $this->ManageGuardianRepositoryInterface->update($request, $guardian);
            DB::commit();

            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function removePhoto(Request $request)
    {
        try {
            DB::beginTransaction();
            $guardian = Guardian::find($request->id);
            $this->ManageGuardianRepositoryInterface->removePhoto($guardian);
            DB::commit();

            return $this->generalSuccess('Photo Removed Successfully.');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Guardian $guardian)
    {
        $result = $this->ManageGuardianRepositoryInterface->destroy($guardian);
        if ($result != 1 && $result == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $result ? $this->generalSuccess(config('constants.DATA_DELETED')) : $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    //TODO:: need to disscuss on login in Repo --- because we need Student Interface in this logic
    public function importStudentGuardians(StudentGuardianRequest $request)
    {
        try {
            DB::beginTransaction();
            if ($request->hasFile('file')) {
                $excelFileData = readExcelFile($request, 'file');

                $noStudents = [];
                $fatherFullData = [];
                $motherFullData = [];
                foreach ($excelFileData as $key => $value) {
                    $student = Student::with(['studentAddress.addressType'])->where(['student_id' => $value['Student ID']])->first();
                    //check if there is Null student then save and return the ids
                    if (!$student) {
                        $noStudents[] = $value;
                        continue;
                    }


                    if (isset($value['Father Name'])  && !empty($value['Father Name'])) {
                        $fatherFullData[] = $this->fatherData($student, $value);
                    }
                    if (isset($value['Mother Name']) && !empty($value['Mother Name'])) {
                        $motherFullData[] = $this->motherData($student, $value);
                    }
                }

                $data['notRegisteredStudent'] = $noStudents;
                $data['parents'] = array_merge($fatherFullData, $motherFullData);

                if (count($data['parents'])) {
                    $parents = $data['parents'];
                    foreach ($parents as $key => $row) {
                        $guardianRequest = new Request($row);
                        $guardian = $this->saveGuardianInfo($guardianRequest);
                        if ($guardian) {
                            $this->linkGuardianWithStudent($guardian, $guardianRequest);
                        }
                    }
                }
            }

            DB::commit();

            return $this->successResponse($data, 'Data imported successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    //save Guardian
    public function saveGuardianInfo($request)
    {
        return $this->ManageGuardianRepositoryInterface->store($request);
    }

    /**
     * link guardian with Student
     */
    protected function linkGuardianWithStudent($guardian, $request)
    {
        $value = $request->all();
        $studentGuardian = [
            'student_id' => $value['student_id'],
            'guardian_id' => $guardian->id,
            'import' => $value['import'],
            'email' => $value['email'],
            'relationship_id' => $value['relationship_id'],
            'is_emergency_contact' => 1,
            'is_authorized_to_pickup' => 1,
            'allow_portal_access' => (isset($value['email']) && !empty($value['email'])) ? 1 : 0,
            'invoice_and_payment_notification' => 1,
            'organization_id' => $value['organization_id'],
        ];
        $gurdianRequest = new Request($studentGuardian);
        $this->StudentRepositoryInterface->addStudentGuardian($gurdianRequest);


        if (isset($value['siblings']) && count($value['siblings']) > 0) {
            foreach ($value['siblings'] as $key => $row) {
                $student = Student::find($row);

                $siblingGuardian = [
                    'student_id' => $row,
                    'guardian_id' => $guardian->id,
                    'import' => $value['import'],
                    'email' => $value['email'],
                    'relationship_id' => $value['relationship_id'],
                    'is_emergency_contact' => 1,
                    'is_authorized_to_pickup' => 1,
                    'allow_portal_access' => (isset($value['email']) && !empty($value['email'])) ? 1 : 0,
                    'invoice_and_payment_notification' => 1,
                    'organization_id' => $student->organization_id,
                ];
                $siblingGurdianRequest = new Request($siblingGuardian);
                $this->StudentRepositoryInterface->addStudentGuardian($siblingGurdianRequest);

                $studentOrganizationId = $student->organization_id;
                $this->syncStudentGuardianOrganizations($guardian, $studentOrganizationId);
            }
        }
    }
    public function importStudents(StudentGuardianRequest $request)
    {
        try {
            DB::beginTransaction();
            if ($request->hasFile('file')) {
                $excelFileData = readExcelFile($request, 'file');

                $invalidStudents = [];
                $enrollmentData = [];
                $classStudents = [];
                $studentGuardians = [];
                // $enrolledData = null;
                $gradeLevels = $this->gradeLevels();
                $organizationGradeLevels = $this->organizationGradeLevels();

                foreach ($excelFileData as $key => $value) {

                    $validate = $this->validateStudent($value);
                    if ($validate['error']) {
                        $value['error'] = $validate['msg'];
                        $invalidStudents[] = $value;

                       // continue;
                    }

                    /** STUDENT PERSONAL INFORMATION */
                    $studentPersonalInformation = $this->studentPersonalInfo($value);
                    $requestStudentPersonal = new Request($studentPersonalInformation);
                    $student = $this->StudentRepositoryInterface->addStudentPersonalInfo($requestStudentPersonal);
                    /** END PERSONAL INFORMATION */
                    if ($student) {
                        /** ENROLLMENT SECTION */
                        $enrolledData = $this->getEnrollmentDataOnce($value, $gradeLevels, $organizationGradeLevels);

                        if ($enrolledData) {
                            $enrollmentData = [
                                'student_id' => $student->id,
                                ...$enrolledData,
                            ];
                            $requestEnrollmentData = new Request($enrollmentData);
                            $this->StudentRepositoryInterface->addStudentEnrollment($requestEnrollmentData);
                        }
                        /** END ENROLLMENT */

                        /** STUDENT MEDICAL INFORMATION */
                        if (isset($value['Allergies']) && !empty($value['Allergies'])) {
                            $medical = $this->studentMedicalData($value['Allergies']);
                            $medicalData = [
                                'student_id' => $student->id,
                                ...$medical,
                            ];
                            $requestMedicalData = new Request($medicalData);
                           $this->StudentRepositoryInterface->addStudentMedicalInfo($requestMedicalData);
                        }
                        /** END MEDICAL INFORMATION */

                        /** STUDENT FATHER INFORMATION */
                        if (isset($value['Father Name']) && !empty($value['Father Name']) && isset($value['Father Email']) && !empty($value['Father Email'])) {
                            $fatherData = $this->fatherData($student, $value);
                            $fatherRequestData = new Request($fatherData);
                            $fatherGuardian = $this->saveGuardianInfo($fatherRequestData);
                            if ($fatherGuardian) {
                                $this->linkGuardianWithStudent($fatherGuardian, $fatherRequestData);
                                $studentGuardians[] = $fatherGuardian->id;
                            }
                        }
                        /** END FATHER INFORMATION */

                        /** STUDENT MOTHER INFORMATION */
                        if (isset($value['Mother Name']) && !empty($value['Mother Name']) && isset($value['Mother Email']) && !empty($value['Mother Email'])) {
                            $motherData = $this->motherData($student, $value);
                            $motherRequestData = new Request($motherData);
                            $motherGuardian = $this->saveGuardianInfo($motherRequestData);
                            if ($motherGuardian) {
                                $this->linkGuardianWithStudent($motherGuardian, $motherRequestData);
                                $studentGuardians[] = $motherGuardian->id;
                            }
                        }
                        /** END MOTHER INFORMATION */

                        if ($student && isset($value['Class ID']) && !empty($value['Class ID'])) {
                            /** STUDENT CLASSES */
                            $classStudents[] = [
                                'student_id' => $student->id,
                                'manage_class_id' => $value['Class ID'],
                                'enrollment_date' => $enrollmentData['status_date'],
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                        /** END STUDENT CLASSES */
                    }
                }
                /** LINK STUDENT WITH CLASS */
                if (count($classStudents)) {
                    ClassStudent::insert($classStudents);
                }
                /** END STUDENT CLASS */
                $studentGuardians = array_unique($studentGuardians);
                if (count($studentGuardians)) {
                  $this->guardianStudentsMakeSiblings($studentGuardians);
                }
            }

            DB::commit();

            return $this->successResponse($invalidStudents, 'Data imported successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    protected function guardianStudentsMakeSiblings($studentGuardians)
    {
        foreach ($studentGuardians as $studentGuardian) {
            $guardianStudents = Guardian::with('students')->find($studentGuardian);

            if ($guardianStudents && count($guardianStudents->students) > 1) {
                $studentIds = $guardianStudents->students->pluck('id')->all();

                foreach ($studentIds as $studentId) {
                    $siblingIds = array_diff($studentIds, [$studentId]);

                    $studentData = Student::find($studentId);
                    $studentData->siblings()->syncWithoutDetaching($siblingIds);
                }
            }
        }
    }

    public function importStudentSiblings(StudentGuardianRequest $request)
    {
        try {
            DB::beginTransaction();
            if ($request->hasFile('file')) {
                $excelFileData = readExcelFile($request, 'file');

                $invalidStudentsSiblings = [];
                foreach ($excelFileData as $key => $value) {

                    if (!isset($value['Sibling ID']) || empty($value['Sibling ID'])) continue;
                    $validate = $this->importStudentSiblingsValidation($value);
                    if ($validate['error']) {
                        $value['error'] = $validate['msg'];
                        $invalidStudentsSiblings[] = $value;
                        continue;
                    }
                    $student = Student::where(['student_id' => $value['Student ID']])->first();
                    $this->studentSiblingData($student, $value);
                }
            }
            DB::commit();
            return $this->successResponse($invalidStudentsSiblings, 'Data imported successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function studentSiblingData($student, $value)
    {
        $siblingIdExcel = explode(',', $value['Sibling ID']);
        $siblingIds = Student::whereIn('student_id', $siblingIdExcel)->pluck('id')->toArray();
        $studentSiblings = [
            'student_id' => $student->id,
            'sibling_ids' => $siblingIds
        ];
        $studentSiblingsRequest = new Request($studentSiblings);
        return $this->StudentRepositoryInterface->addStudentSibiling($studentSiblingsRequest);
    }
}
