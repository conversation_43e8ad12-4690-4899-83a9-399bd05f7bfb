<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AssessmentLevel;
use App\Http\Requests\StoreAssessmentLevelRequest;
use App\Http\Requests\UpdateAssessmentLevelRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\AssessmentLevelRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\DB;

class AssessmentLevelController extends Controller
{
    use ApiResponse;

    private $AssessmentLevelRepositoryInterface;
    public function __construct(AssessmentLevelRepositoryInterface $AssessmentLevelRepositoryInterface)
    {
        $this->AssessmentLevelRepositoryInterface = $AssessmentLevelRepositoryInterface;
    }
    
    public function index()
    {
        $state = $this->AssessmentLevelRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }
    
    public function store(StoreAssessmentLevelRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->AssessmentLevelRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {        
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }
    public function show($AssessmentLevel)
    {
        $state = $this->AssessmentLevelRepositoryInterface->show($AssessmentLevel);
        return response()->json($state, Response::HTTP_OK);
    }
    public function update(UpdateAssessmentLevelRequest $request, AssessmentLevel $AssessmentLevel)
    {
        try {
            DB::beginTransaction();
            $this->AssessmentLevelRepositoryInterface->update($request, $AssessmentLevel);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }
    public function destroy($AssessmentLevel)
    {
        try {
            DB::beginTransaction();
            $this->AssessmentLevelRepositoryInterface->destroy($AssessmentLevel);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }       
    }
            
}