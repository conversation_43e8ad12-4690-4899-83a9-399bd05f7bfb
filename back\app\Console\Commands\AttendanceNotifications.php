<?php

namespace App\Console\Commands;

use App\Models\{
    Attendance,
    AttendanceCode,
    Module
};
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Traits\AttendanceTrait;
use Illuminate\Support\Facades\{
    Log,
    DB
};

class AttendanceNotifications extends Command
{
    use AttendanceTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This job is for attendance notifications for absentees and late';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $system_time = DB::table('system_settings')
            ->where('name', 'absentees_notification')
            ->value('value');

        if (!$system_time) return;

        $current_time = now()->format('H:i');
        // Log::channel('failed_emails')->info([$current_time,$system_time]);

        if ($current_time !== $system_time) return;
        

        $currentDate = Carbon::now();

        $attendances = Attendance::whereNull('notification_sent_at')
            ->whereIn('attendance_code_id',[AttendanceCode::EXCUSED_ABSENCE, AttendanceCode::UNEXCUSED_ABSENCE, AttendanceCode::LATE])
            ->get();

        // Log::channel('failed_emails')->info([AttendanceCode::EXCUSED_ABSENCE, AttendanceCode::UNEXCUSED_ABSENCE, AttendanceCode::LATE]);
        // Log::channel('failed_emails')->info($attendances);
        
        if (count($attendances)) {
            foreach ($attendances as $row) {

                $is_email = Module::where('name', 'Attendance')->first();
                if ($is_email && $is_email->email_notification === 1) {
                    $this->emailNotificationToGuardian($row);
                }

                Attendance::where('id', $row->id)
                    ->update(['notification_sent_at' => $currentDate]);
            }
        }

        return 0;
        // return Command::SUCCESS;
    }
}
