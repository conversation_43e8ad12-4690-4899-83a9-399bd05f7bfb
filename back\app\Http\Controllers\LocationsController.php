<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Location;
use App\Http\Requests\StoreLocationsRequest;
use App\Http\Requests\UpdateLocationsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\LocationsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class LocationsController extends Controller
{
    use ApiErrorResponse;

    private $LocationsRepositoryInterface;


    public function __construct(LocationsRepositoryInterface $LocationsRepositoryInterface)
    {
        $this->LocationsRepositoryInterface = $LocationsRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->LocationsRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLocationsRequest $request)
    {
        $state = $this->LocationsRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Locations)
    {
        $state = $this->LocationsRepositoryInterface->show($Locations);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateLocationsRequest $request, Location $Locations)
    {
        $state = $this->LocationsRepositoryInterface->update($request, $Locations);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Locations)
    {
        $state = $this->LocationsRepositoryInterface->destroy($Locations);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
}
