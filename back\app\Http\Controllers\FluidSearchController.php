<?php

namespace App\Http\Controllers;

use App\Http\Resources\SearchResource\GuardianResource;
use App\Http\Resources\SearchResource\InvoiceResource;
use App\Http\Resources\SearchResource\ModuleResource;
use App\Http\Resources\SearchResource\PaymentResource;
use App\Http\Resources\SearchResource\StudentResource;
use App\Http\Resources\SearchResource\TeacherResource;
use App\Http\Resources\SearchResource\UserResource;
use App\Models\Guardian;
use App\Models\Invoice;
use App\Models\Module;
use App\Models\Payment;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class FluidSearchController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $query = $request->input('search');
        $searchDataArray = [];

        $teacherResults = Teacher::search($query)->get();
        $guardianResults = Guardian::search($query)->get();
        $invoiceResults = Invoice::search($query)->get();
        $paymentResults = Payment::search($query)->get();
        $StudentResults = Student::search($query)->get();
        $userResults = User::search($query)->get();
        $moduleResults = Module::search($query)
            ->where('application_id', 1)
            ->where('visible_status', 1)
            ->where('isGroupTitle', 0)
            ->whereNotIn('id', function ($subQuery) {
                $subQuery->select('parent_id')
                    ->from('modules')
                    ->whereNotNull('parent_id');
            })
            ->get();

        $invoiceCollection = InvoiceResource::collection($invoiceResults);
        $paymentCollection = PaymentResource::collection($paymentResults);
        $teacherCollection = TeacherResource::collection($teacherResults);
        $guardianCollection = GuardianResource::collection($guardianResults);
        $studentCollection = StudentResource::collection($StudentResults);
        $userCollection = UserResource::collection($userResults);
        $moduleCollection = ModuleResource::collection($moduleResults);

        $searchDataArray[] = [
            'value' => 'module',
            'title' => 'Modules',
            'tabs' => $moduleCollection,
        ];

        $searchDataArray[] = [
            'value' => 'Invoice',
            'title' => 'Invoices',
            'tabs' => $invoiceCollection,
        ];

        $searchDataArray[] = [
            'value' => 'payment',
            'title' => 'Payments',
            'tabs' => $paymentCollection,
        ];

        $searchDataArray[] = [
            'value' => 'teacher',
            'title' => 'Teachers',
            'tabs' => $teacherCollection,
        ];

        $searchDataArray[] = [
            'value' => 'guardian',
            'title' => 'Guardians',
            'tabs' => $guardianCollection,
        ];

        $searchDataArray[] = [
            'value' => 'student',
            'title' => 'Students',
            'tabs' => $studentCollection,
        ];

        $searchDataArray[] = [
            'value' => 'users',
            'title' => 'Users',
            'tabs' => $userCollection,
        ];

        return response()->json($searchDataArray, Response::HTTP_OK);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
