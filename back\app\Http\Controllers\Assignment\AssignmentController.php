<?php

namespace App\Http\Controllers\Assignment;

use App\Models\Teacher;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use App\Models\CourseTeacher;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Assignment\Assignment;
use App\Http\Requests\Assignment\StoreAssignmentRequest;
use App\Http\Requests\Assignment\UpdateAssignmentRequest;
use App\Repositories\Interfaces\Assignment\AssignmentRepositoryInterface;

class AssignmentController extends Controller
{
    use ApiResponse;

    private $AssignmentRepositoryInterface;

    public function __construct(AssignmentRepositoryInterface $AssignmentRepositoryInterface)
    {
        $this->AssignmentRepositoryInterface = $AssignmentRepositoryInterface;
    }

    public function index(Request $request)
    {

        /**this is classCourId */
        // $courseTeacherId = $this->getCourseTeacherId($request->class_course_id);
        // if (!$courseTeacherId) return $this->generalError(config('constants.UNAUTHORIZED'));

        /**merge course teacher id into Request */
        // $request->merge(['course_teacher_id' => $courseTeacherId]);

        $result = $this->AssignmentRepositoryInterface->index($request);

        return response()->json($result, Response::HTTP_OK);
    }

    protected function getCourseTeacherId($classCourseId)
    {
        //TODO: Need to more Discussion for make dynamic Teacher
        // any other teacher can create assignment for Other Teacher
        /**get current Teacher ID on the base of user logedIn*/
        // if (isset(Auth::user()->teacherData) && Auth::user()->teacherData != null) {
        //     $teacherId = Auth::user()->teacherData['id'];
        // }
        $teacherId = Teacher::where(['user_id' => auth()->user()->id])->first()?->id;
        if (!$teacherId) {
            return false;
        }

        $courseTeacherId = CourseTeacher::where(['class_course_id' => $classCourseId, 'is_active' => 1, 'teacher_id' => $teacherId])->first()?->id;
        if (!$courseTeacherId) {
            return false;
        }

        return $courseTeacherId;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAssignmentRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAssignmentRequest $request)
    {
        /**this is classCourId */
        $courseTeacherId = $this->getCourseTeacherId($request->class_course_id);
        if (!$courseTeacherId) {
            return $this->generalError(config('constants.UNAUTHORIZED'));
        }

        /**merge course teacher id into Request */
        $request->merge(['course_teacher_id' => $courseTeacherId]);
        try {
            DB::beginTransaction();
            $this->AssignmentRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
    public function show(Assignment $assignment)
    {
        try {
            $result = $this->AssignmentRepositoryInterface->show($assignment);
            return response()->json($result, Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function removeSingleAssignee(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->AssignmentRepositoryInterface->removeSingleAssignee($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function addMoreStudentsIntoAssignment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'class_student_ids' => 'required|array|min:1',
        ], [
            'class_student_ids.required' => 'Student field is required.',
            'class_student_ids.min' => 'At least one student must be selected.',
        ]);

        if ($validator->fails()) {
            return $this->generalError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $result = $this->AssignmentRepositoryInterface->addMoreStudentsIntoAssignment($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function removeSingleRubric(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->AssignmentRepositoryInterface->removeSingleRubric($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function addMoreRubricsIntoAssignment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'assignment_rubrics' => 'required',
        ], [
            'assignment_rubrics.required' => 'Rubric field is required.'
        ]);

        if ($validator->fails()) {
            return $this->generalError($validator->errors()->first());
        }
        try {
            DB::beginTransaction();
            $result = $this->AssignmentRepositoryInterface->addMoreRubricsIntoAssignment($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function edit(Assignment $assignment)
    {
        return $assignment;
    }
    public function updateAssignment(UpdateAssignmentRequest $request)
    {
        /**this is classCourId */
        $courseTeacherId = $this->getCourseTeacherId($request->class_course_id);
        if (!$courseTeacherId) {
            return $this->generalError(config('constants.UNAUTHORIZED'));
        }

        /**merge course teacher id into Request */
        $request->merge(['course_teacher_id' => $courseTeacherId]);

        $assignment = Assignment::find($request->id);
        $result = $this->AssignmentRepositoryInterface->update($request, $assignment);

        return $result ? $this->generalSuccess(config('constants.DATA_UPDATED'))
            : $this->generalError(config('constants.GENERAL_ERROR'));
    }
    public function deleteAssignment(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->AssignmentRepositoryInterface->deleteAssignment($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**change assignment status */
    public function changeAssignmentStatus(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->assignmentStatus($request);

        return $result ? $this->generalSuccess(config('constants.DATA_UPDATED'))
            : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**mark assignment  */
    public function markAssignment(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->markAssignment($request);

        return $result ? $this->generalSuccess(config('constants.DATA_UPDATED'))
            : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**re-assign assignment to student */
    public function reAssigneAssignment(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->reAssigneAssignment($request);

        return $result ? $this->generalSuccess(config('constants.DATA_UPDATED'))
            : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**save assignment comment */
    public function saveAssignmentComment(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->saveAssignmentComment($request);

        return response()->json('saved!!!', Response::HTTP_OK);
    }

    /**get assignment Comments */
    public function getAssignmentComments(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->getAssignmentComments($request);

        return response()->json($result, Response::HTTP_OK);
    }
    public function singleStudentSaveRubricMarks(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->AssignmentRepositoryInterface->singleStudentSaveRubricMarks($request);
            DB::commit();
            return  $this->generalSuccess(config('constants.DATA_INSERTED'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    /**get all assignees by status */
    // public function getAssigneesByStatus(Request $request){
    //     $result = $this->AssignmentRepositoryInterface->getAssigneesByStatus($request);
    //     return response()->json($result, Response::HTTP_OK);
    // }

    // student or parent App
    //:: TODO :: Discuss with Team for better place of parent App methods

    public function singleStudentAssignments(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->singleStudentAssignments($request);

        return response()->json($result, Response::HTTP_OK);
    }

    //# submit student assignment
    public function submitAssignment(Request $request)
    {
        $result = $this->AssignmentRepositoryInterface->submitAssignment($request);

        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED'))
            : $this->generalError(config('constants.GENERAL_ERROR'));
    }
}
