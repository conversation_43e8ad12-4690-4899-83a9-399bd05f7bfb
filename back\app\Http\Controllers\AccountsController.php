<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Accounts;
use App\Http\Requests\StoreAccountsRequest;
use App\Http\Requests\UpdateAccountsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\AccountsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class AccountsController extends Controller
{
    use ApiErrorResponse;

    private $AccountsRepositoryInterface;

    
            public function __construct(AccountsRepositoryInterface $AccountsRepositoryInterface)
            {
                $this->AccountsRepositoryInterface = $AccountsRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->AccountsRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreAccountsRequest $request)
            {
                $state = $this->AccountsRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($Accounts)
            {
                $state = $this->AccountsRepositoryInterface->show($Accounts);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateAccountsRequest $request, Accounts $Accounts)
            {
                $state = $this->AccountsRepositoryInterface->update($request, $Accounts);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($Accounts)
            {
                $state = $this->AccountsRepositoryInterface->destroy($Accounts);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}