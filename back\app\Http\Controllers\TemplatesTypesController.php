<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTemplatesTypesRequest;
use App\Http\Requests\UpdateTemplatesTypesRequest;
use App\Models\TemplatesTypes;
use App\Repositories\Interfaces\TemplatesTypesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class TemplatesTypesController extends Controller
{
    use ApiErrorResponse;

    private $TemplatesTypesRepositoryInterface;

    public function __construct(TemplatesTypesRepositoryInterface $TemplatesTypesRepositoryInterface)
    {
        $this->TemplatesTypesRepositoryInterface = $TemplatesTypesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TemplatesTypesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTemplatesTypesRequest $request)
    {
        $state = $this->TemplatesTypesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($TemplatesTypes)
    {
        $state = $this->TemplatesTypesRepositoryInterface->show($TemplatesTypes);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTemplatesTypesRequest $request, TemplatesTypes $TemplatesTypes)
    {
        $state = $this->TemplatesTypesRepositoryInterface->update($request, $TemplatesTypes);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TemplatesTypes)
    {
        $state = $this->TemplatesTypesRepositoryInterface->destroy($TemplatesTypes);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
