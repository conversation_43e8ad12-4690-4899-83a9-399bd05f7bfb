<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateServiceProvider extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    public function __construct()
    {
        parent::__construct();
    }

    protected $signature = 'app:generate-service-provider {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a custom service provider with dynamic content: ## Ejaz Hanif +923018108844 ##';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $serviceProviderName = $this->argument('name');
        $modelName = $serviceProviderName;
        $serviceProviderContent = $this->generateServiceProviderContent($serviceProviderName, $modelName);

        // Determine the path where you want to save the service provider file
        $serviceProviderPath = app_path("Providers/{$serviceProviderName}ServiceProvider.php");

        // Check if the service provider file already exists
        if (file_exists($serviceProviderPath)) {
            $this->error("Service provider '{$serviceProviderName}' already exists.");

            return 1;
        }

        // Create the service provider file with the desired content
        file_put_contents($serviceProviderPath, $serviceProviderContent);

        $this->info("Service provider '{$serviceProviderName}' created successfully.");

        return 0;
    }

    //
    private function generateServiceProviderContent($serviceProviderName, $modelName)
    {
        $content = "<?php\n\n";
        $content .= "namespace App\\Providers;\n\n";
        $content .= "use Illuminate\\Support\\ServiceProvider;\n";
        $content .= "use App\\Models\\$modelName;\n"; // Use the dynamic model name
        $content .= "use App\\Observers\\{$modelName}Observer;\n";
        $content .= "use App\\Repositories\\Interfaces\\{$modelName}RepositoryInterface;\n";
        $content .= "use App\\Repositories\\{$modelName}Repository;\n\n";
        $content .= "class {$serviceProviderName}ServiceProvider extends ServiceProvider\n";
        $content .= "{\n";
        $content .= "    public function register(): void\n";
        $content .= "    {\n";
        $content .= "        \$this->app->bind({$modelName}RepositoryInterface::class, {$modelName}Repository::class);\n";
        $content .= "    }\n\n";
        $content .= "    public function boot(): void\n";
        $content .= "    {\n";
        $content .= "        {$modelName}::observe({$modelName}Observer::class);\n";
        $content .= "    }\n";
        $content .= "}\n";

        return $content;
    }
}
