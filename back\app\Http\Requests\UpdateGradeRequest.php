<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateGradeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'name' => [
                'required',
                'max:255',
                $Id ? Rule::unique('grades')->ignore($Id) : 'unique:grades',
            ],
            'percent_from' => 'required|numeric|min:0|max:100',
            'percent_to' => 'required|numeric|min:0|max:100',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The grade name is required.',
            'name.max' => 'The grade name must not exceed 255 characters.',
            'name.unique' => 'The grade name is already taken. Please choose a different name.',
            'percent_from.required' => 'The lower percentage limit is required.',
            'percent_from.numeric' => 'The lower percentage limit must be a number.',
            'percent_from.min' => 'The lower percentage limit must be at least 0.',
            'percent_from.max' => 'The lower percentage limit must not exceed 100.',
            'percent_to.required' => 'The upper percentage limit is required.',
            'percent_to.numeric' => 'The upper percentage limit must be a number.',
            'percent_to.min' => 'The upper percentage limit must be at least 0.',
            'percent_to.max' => 'The upper percentage limit must not exceed 100.',
        ];
    }
}
