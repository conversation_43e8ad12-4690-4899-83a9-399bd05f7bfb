<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ReceivablesReport;
use App\Http\Requests\StoreReceivablesReportRequest;
use App\Http\Requests\UpdateReceivablesReportRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\ReceivablesReportRepositoryInterface;
use App\Traits\ApiErrorResponse;

class ReceivablesReportController extends Controller
{
    use ApiErrorResponse;

    private $ReceivablesReportRepositoryInterface;


    public function __construct(ReceivablesReportRepositoryInterface $ReceivablesReportRepositoryInterface)
    {
        $this->ReceivablesReportRepositoryInterface = $ReceivablesReportRepositoryInterface;
    }

    public function getStudentReceivableReport(Request $request)
    {
        $state = $this->ReceivablesReportRepositoryInterface->getStudentReceivableReport($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentInvoices(Request $request)
    {
        $state = $this->ReceivablesReportRepositoryInterface->getStudentInvoices($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentPayments(Request $request)
    {
        $state = $this->ReceivablesReportRepositoryInterface->getStudentPayments($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function studentReceivableSummaryReport(Request $request)
    {
        $state = $this->ReceivablesReportRepositoryInterface->studentReceivableSummaryReport($request);
        return response()->json($state, Response::HTTP_OK);
    }
}
