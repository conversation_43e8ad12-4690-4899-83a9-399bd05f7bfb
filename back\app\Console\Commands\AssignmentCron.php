<?php

namespace App\Console\Commands;

use App\Events\AssignmentEmailEvent;
use App\Models\Assignment\{
    Assignment
};
use Carbon\Carbon;
use Illuminate\Console\Command;

class AssignmentCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignment:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This job is for schedule assignment to assigned';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $currentDate = Carbon::now();

        $assignments = Assignment::where('assignment_status', 'scheduled')->whereDate('schedule_date', '<=', $currentDate)->get();
        if (count($assignments)) {
            foreach ($assignments as $row => $value) {
                /**call assignment Event for Notification */
                event(new AssignmentEmailEvent($value->id));

                /** update assignment status after assigned */
                Assignment::where(['id' => $value->id, 'assignment_status' => 'scheduled'])
                    ->update(['assignment_status' => 'assigned']);
            }
        }

        return 0;
        // return Command::SUCCESS;
    }
}
