<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreIdentificationTypeRequest;
use App\Http\Requests\UpdateIdentificationTypeRequest;
use App\Models\IdentificationType;
use App\Repositories\Interfaces\IdentificationTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\File;

class IdentificationTypeController extends Controller
{
    use ApiErrorResponse;

    private $IdentificationTypeRepositoryInterface;

    public function __construct(IdentificationTypeRepositoryInterface $IdentificationTypeRepositoryInterface)
    {
        $this->IdentificationTypeRepositoryInterface = $IdentificationTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->IdentificationTypeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreIdentificationTypeRequest $request)
    {
        $state = $this->IdentificationTypeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($IdentificationType)
    {
        $state = $this->IdentificationTypeRepositoryInterface->show($IdentificationType);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateIdentificationTypeRequest $request, IdentificationType $IdentificationType)
    {
        $state = $this->IdentificationTypeRepositoryInterface->update($request, $IdentificationType);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($IdentificationType)
    {
        $state = $this->IdentificationTypeRepositoryInterface->destroy($IdentificationType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function secureFileUpload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240'
        ]);

        $file = $request->file('file');
        $filename = uniqid() . '_' . $file->getClientOriginalName();

        $destinationPath =  env('AWS_BUCKET_SCHOOL') . "/secure_uploads/";
        $folderName =  "secure_uploads/";
        $path = Storage::disk('s3')->putFileAs($destinationPath, $file, $filename, 'private');

        return response()->json([
            'message' => 'File uploaded successfully.',
            'filename' => $filename,
            'path' => $path,
        ]);
    }


    public function secureFileDownload(Request $request)
    {
        $filename = $request->get('filename');

        if (!$filename) {
            return response()->json(['error' => 'Filename is required.'], 400);
        }

        // Path on S3
        $s3Path = env('AWS_BUCKET_SCHOOL') . '/secure_uploads/' . $filename;

        try {
            // Optional: verify file exists
            // if (!Storage::disk('s3')->exists($s3Path)) {
            //     Log::warning('File not found in S3', ['filePath' => $s3Path]);
            //     return response()->json(['error' => 'File not found on S3'], 404);
            // }

            // Get file content from S3
            $fileContents = Storage::disk('s3')->get($s3Path);

            // Create a unique filename to prevent collision
            $tempFilename = Str::random(20) . '_' . $filename;

            $fileDestination = 'temporary-downloads/' . $tempFilename;
            // Save directly into public folder
            $publicPath = public_path($fileDestination);

            // Ensure the directory exists
            File::ensureDirectoryExists(public_path('temporary-downloads'));

            // Save file
            File::put($publicPath, $fileContents);

            // Return public URL
            return response()->json([
                'message' => 'File is ready for download.',
                'filename' => $filename,
                'download_url' => asset('public/' . $fileDestination),
            ]);
        } catch (\Exception $e) {
            Log::error('S3 File Download Error', [
                'error' => $e->getMessage(),
                'filePath' => $s3Path,
            ]);

            return response()->json([
                'error' => 'S3 download failed.',
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
