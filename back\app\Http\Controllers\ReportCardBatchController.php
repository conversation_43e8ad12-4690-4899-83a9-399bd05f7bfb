<?php

namespace App\Http\Controllers;

use PDF;
use Carbon\Carbon;
use App\Models\Student;
use App\Models\ChargeRate;
use App\Models\SchoolYear;
use App\Models\ClassCourse;
use App\Models\ManageClass;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use App\Models\StudentCharge;
use Illuminate\Http\Response;
use App\Models\RecurringCharge;
use App\Models\ReportCardBatch;
use App\Traits\ReportCardTrait;
use App\Models\ClassCourseStrand;
use App\Models\StudentReportCard;
use App\Models\ReportCardSubBatch;
use Illuminate\Support\Facades\DB;
use App\Models\ReportCardTypeGrade;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Traits\ReportCardStrategyTrait;
use App\Http\Requests\StoreReportCardBatchRequest;
use App\Http\Requests\UpdateReportCardBatchRequest;

use App\Http\Requests\DownloadReportCardBatchRequest;
use App\Repositories\Interfaces\ReportCardBatchRepositoryInterface;
// use Illuminate\Support\Facades\Storage;

class ReportCardBatchController extends Controller
{
    use ApiResponse, ReportCardTrait, ReportCardStrategyTrait;
    private $ReportCardBatchRepositoryInterface;

    public function __construct(ReportCardBatchRepositoryInterface $ReportCardBatchRepositoryInterface)
    {
        $this->ReportCardBatchRepositoryInterface = $ReportCardBatchRepositoryInterface;
    }

    public function index()
    {
        $state = $this->ReportCardBatchRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    public function getGradeSubBatches(Request $request)
    {
        $state = $this->ReportCardBatchRepositoryInterface->getGradeSubBatches($request);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreReportCardBatchRequest $request)
    {
        try {
            $reportCardTypeId = $request->report_card_type_id;
            $reportCardTypeGrades = ReportCardTypeGrade::where('report_card_type_id', $reportCardTypeId)->pluck('organization_grade_level_id')->toArray();
            if (empty($reportCardTypeGrades)) {
                throw new \Exception('No grade levels found for the selected report card type.');
            }
            $request->merge(['gradeLevel' => $reportCardTypeGrades]);

            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->store($request);
            DB::commit();
            return $this->generalSuccess('Batch creation is in progress. You will be notified via email once completed.');
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($ReportCardBatch)
    {
        $result = $this->ReportCardBatchRepositoryInterface->show($ReportCardBatch);
        return response()->json($result, Response::HTTP_OK);
    }

    public function subBatchSingleClass(Request $request)
    {
        $result = $this->ReportCardBatchRepositoryInterface->subBatchSingleClass($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function getSingleClassOnlyStudents(Request $request)
    {
        $result = $this->ReportCardBatchRepositoryInterface->getSingleClassOnlyStudents($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function singleStudentReportCardDetail(Request $request)
    {
        $result = $this->ReportCardBatchRepositoryInterface->singleStudentReportCardDetail($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function preLoadData(Request $request)
    {
        $result = $this->ReportCardBatchRepositoryInterface->preLoadData($request);
        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateReportCardBatchRequest $request, ReportCardBatch $ReportCardBatch)
    {
        try {
            DB::beginTransaction();
            $result = $this->ReportCardBatchRepositoryInterface->update($request, $ReportCardBatch);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ReportCardBatch)
    {
        // $state = $this->ReportCardBatchRepositoryInterface->destroy($ReportCardBatch);
        // if ($state != 1 && $state == 23000) {
        //     return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        // } else {
        //     return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
        //         $this->generalError(config('constants.GENERAL_ERROR'));;
        // }
    }


    //
    public function downloadReportCardBatch(DownloadReportCardBatchRequest $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ReportCardBatchRepositoryInterface->downloadReportCardBatch($request);
            DB::commit();
            if ($request->type == 'class') {
                return $this->successResponse('class', 'You will receive an email with the Report Card');
            }
            return $result;
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function guardianReportCardBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ReportCardBatchRepositoryInterface->guardianReportCardBatch($request);
            DB::commit();
            return $result;
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function holdReportCard(Request $request)
    {
        try {
            DB::beginTransaction();
            StudentReportCard::find($request->id)->update(['is_withheld' => $request->is_withheld]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function approveBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            ReportCardBatch::find($request->id)->update([
                'approved_by' => auth()->user()->id,
                'approved_at' => now(),
            ]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function revertBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            ReportCardBatch::find($request->id)->update([
                'approved_by' => null,
                'approved_at' => null,
            ]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function publishBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            ReportCardBatch::find($request->id)->update([
                'is_published' => 1,
                'publish_date' => now(),
            ]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function approveSubBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            ReportCardSubBatch::where([
                'report_card_batch_id' => $request->report_card_batch_id,
                'manage_class_id' => $request->manage_class_id,
            ])->update([
                'is_approved_by_home_room_teacher' => $request->is_approved_by_home_room_teacher,
                'approved_by' => auth()->user()->id,
                'approved_at' => now(),
            ]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function rollBackApproveSubBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            ReportCardSubBatch::where([
                'report_card_batch_id' => $request->report_card_batch_id,
                'manage_class_id' => $request->manage_class_id,
            ])->update([
                'is_approved_by_home_room_teacher' => 0,
                'approved_by' => NULL,
                'approved_at' => NULL,
            ]);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }


    // generate pdf in browser
    public function generatePdfForTest(Request $request)
    {
        try {
            // $templateName = $this->getPDFTemplateName($request);
            // $request->merge([
            //     'template_name' => $templateName
            // ]);

            // $templateName = 'report-cards.report-card';

            $result = $this->ReportCardBatchRepositoryInterface->generatePdfForTest($request);
            // dd($result);
            $pdfTemplateType = $this->pdfTemplateType($result);
            // dd($pdfTemplateType);
            $pdf = PDF::loadView($pdfTemplateType, $result);
            return $pdf->stream('ReportCard.pdf');
        } catch (\Throwable $th) {
            Log::error($th);
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function recalculateStudentAttendance(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->recalculateStudentAttendance($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function updateStudentReportCardAttendance(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:student_report_cards,id',
            'days_absent' => 'required|integer',
            'total_days_absent' => 'required|integer',
            'days_late' => 'required|integer',
            'total_days_late' => 'required|integer'
        ]);

        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->updateStudentReportCardAttendance($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function bulkApproveAllClasses(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->bulkApproveAllClasses($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function getClasStudents(Request $request)
    {
        $result = $this->getActiveStudentsForClass($request->manage_class_id);
        return response()->json($result, Response::HTTP_OK);
    }
    public function addStudentsIntoBatch(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->addStudentsIntoBatch($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function updateGradeMarks(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->updateGradeMarks($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function updateIsPromoted(Request $request)
    {
        try {
            DB::beginTransaction();
            $this->ReportCardBatchRepositoryInterface->updateIsPromoted($request);
            DB::commit();
            return $this->generalSuccess(config('constants.DATA_UPDATED'));
        } catch (\Throwable $th) {
            Log::error($th);
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    //Parent APP Methods
    public function studentYearWiseReportCard(Request $request)
    {
        $result = $this->ReportCardBatchRepositoryInterface->studentYearWiseReportCard($request);
        return response()->json($result, Response::HTTP_OK);
    }
}
