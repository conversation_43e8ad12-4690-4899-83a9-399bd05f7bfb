<?php

namespace App\Http\Controllers\Assignment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Assignment\StoreAssignmentAttachmentRequest;
use App\Http\Requests\Assignment\UpdateAssignmentAttachmentRequest;
use App\Models\Assignment\AssignmentAttachment;
use App\Traits\ApiResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class AssignmentAttachmentController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAssignmentAttachmentRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAssignmentAttachmentRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(AssignmentAttachment $assignmentAttachment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(AssignmentAttachment $assignmentAttachment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAssignmentAttachmentRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateAssignmentAttachmentRequest $request, AssignmentAttachment $assignmentAttachment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(AssignmentAttachment $assignmentAttachment)
    {
        try {
            DB::beginTransaction();
            $assignmentAttachment->delete();
            DB::commit();

            return response()->json($assignmentAttachment->id, Response::HTTP_OK);
            // return $this->generalSuccess(config('constants.DATA_DELETED'));
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
