<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
class UpdateManageTeacherRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('user_id');

        return [
            'first_name' => 'required|string|max:200',
            'middle_name' => 'nullable|string|max:200',
            'last_name' => 'required|string|max:200',
            'employee_code' => 'nullable|string',
            'preferred_name' => 'required|string|max:200',
            'salutation_id' => 'required',
            'email' => 'required|email|unique:users,email,' . $Id,
            // 'email' => 'required|email|', $Id ? Rule::unique('users')->ignore($Id) : 'unique:users',
            'gender_id' => 'required',
            'enrollment_status_id' => 'required',
            'marital_status_id' => 'nullable',
            'nationality_id' => 'required',
            'notes' => 'nullable|string',
            'addressId' => 'nullable',
            'address_type_id' => 'required',
            'province_id' => 'required',
            'city' => 'required|string|max:200',
            'postal_code' => 'required|string',
            'address_1' => 'required|string|max:255',
            'address_2' => 'nullable|string|max:255',
            'cell_phone' => 'required',
            'fax' => 'nullable|string|max:200',
        ];
    }
}
