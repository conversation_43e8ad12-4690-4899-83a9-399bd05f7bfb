<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreIncidentReportRequest;
use App\Http\Requests\UpdateIncidentReportRequest;
use App\Models\IncidentReport;
use App\Repositories\Interfaces\IncidentReportRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class IncidentReportController extends Controller
{
    use ApiErrorResponse;

    private $IncidentReportRepositoryInterface;

    public function __construct(IncidentReportRepositoryInterface $IncidentReportRepositoryInterface)
    {
        $this->IncidentReportRepositoryInterface = $IncidentReportRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->IncidentReportRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreIncidentReportRequest $request)
    {
        $state = $this->IncidentReportRepositoryInterface->store($request);
        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($IncidentReport)
    {
        $state = $this->IncidentReportRepositoryInterface->show($IncidentReport);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateIncidentReportRequest $request, IncidentReport $IncidentReport)
    {
        $state = $this->IncidentReportRepositoryInterface->update($request, $IncidentReport);

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($IncidentReport)
    {
        $state = $this->IncidentReportRepositoryInterface->destroy($IncidentReport);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function get_participant_data(Request $request)
    {
        $state = $this->IncidentReportRepositoryInterface->get_participant_data($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function delete_participant(Request $IncidentReport)
    {
        $state = $this->IncidentReportRepositoryInterface->delete_participant($IncidentReport);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function saveParticipant(Request $IncidentParticipant)
    {
        $state = $this->IncidentReportRepositoryInterface->saveParticipant($IncidentParticipant);

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function saveIncidentAction(Request $IncidentAction)
    {
        $state = $this->IncidentReportRepositoryInterface->saveIncidentAction($IncidentAction);

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function getIncidentActions(Request $request)
    {
        $state = $this->IncidentReportRepositoryInterface->getIncidentActions($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function saveParticipantAction(Request $IncidentAction)
    {
        $state = $this->IncidentReportRepositoryInterface->saveParticipantAction($IncidentAction);

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function saveOtherParticipant(Request $IncidentAction)
    {
        $state = $this->IncidentReportRepositoryInterface->saveOtherParticipant($IncidentAction);

        return $state['status'] ? $this->successResponse($state['data'], config('constants.DATA_UPDATED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function getIncidentDashboardData(Request $request)
    {
        $state = $this->IncidentReportRepositoryInterface->getIncidentDashboardData($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getIncidentsData(Request $request)
    {
        $state = $this->IncidentReportRepositoryInterface->getIncidentsData($request);

        return response()->json($state, Response::HTTP_OK);
    }
    public function get_student_incident_data(Request $request)
    {
        $state = $this->IncidentReportRepositoryInterface->get_student_incident_data($request);

        return response()->json($state, Response::HTTP_OK);
    }
}
