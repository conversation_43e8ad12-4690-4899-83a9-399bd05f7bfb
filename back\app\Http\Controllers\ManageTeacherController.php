<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageTeacherRequest;
use App\Http\Requests\UpdateManageTeacherRequest;
use App\Models\ManageTeacher;
use App\Repositories\Interfaces\ManageTeacherRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class ManageTeacherController extends Controller
{
    use ApiErrorResponse;

    private $ManageTeacherRepositoryInterface;

    public function __construct(ManageTeacherRepositoryInterface $ManageTeacherRepositoryInterface)
    {
        $this->ManageTeacherRepositoryInterface = $ManageTeacherRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageTeacherRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageTeacherRequest $request)
    {
        
        $state = $this->ManageTeacherRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ManageTeacher)
    {
        $state = $this->ManageTeacherRepositoryInterface->show($ManageTeacher);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageTeacherRequest $request, ManageTeacher $ManageTeacher)
    {
        try {
            DB::beginTransaction();
            $teacher = ManageTeacher::find($request->id);
            $this->ManageTeacherRepositoryInterface->update($request, $teacher);
            DB::commit();
            return $this->successResponse(false, config('constants.DATA_UPDATED'));
        }  catch (\Exception $exp) {
            DB::rollBack();     
            return $this->generalError(config('constants.GENERAL_ERROR'));       
        }            
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageTeacher)
    {
        $state = $this->ManageTeacherRepositoryInterface->destroy($ManageTeacher);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function getGender()
    {
        $state = $this->ManageTeacherRepositoryInterface->getGender();

        return response()->json($state, Response::HTTP_OK);
    }
}
