<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentPersonalInfoReuest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->get('id');

        return [
            'first_name' => 'required',
            'last_name' => 'required',
            'student_email' => [
                'nullable',
                'email',
                // $id ? Rule::unique('students')->ignore($id) : 'unique:students',
            ],
            'gender_id' => 'required',
            'nationality_id' => 'required',
            'birth_date' => 'required',
            'student_id' => [
                'required',
                $id ? Rule::unique('students')->ignore($id) : 'unique:students'
            ],
            'provincial_student_number' => [
                'required',
                $id ? Rule::unique('students')->ignore($id) : 'unique:students'
            ],
            'address_type_id' => 'required',
            'province_id' => 'required',
            'city' => 'required',
            'postal_code' => 'required',
            'address_1' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'The first name is required.',
            'last_name.required' => 'The last name is required.',
            // 'student_email.required' => 'The email is required.',
            'gender_id.required' => 'Gender is required.',
            'nationality_id.required' => 'Nationality is required.',
            'birth_date.required' => 'Date of birth is required.',
            'student_id.required' => 'Student ID is required.',
            'provincial_student_number.required' => 'Provincial student number is required.',
            'address_type_id.required' => 'Address type is required.',
            'province_id.required' => 'Province is required.',
            'city.required' => 'City is required.',
            'postal_code.required' => 'Postal code is required.',
            'address_1.required' => 'Address is required.',
            'student_id.unique' => 'The student ID has already been registered.',
            'provincial_student_number.unique' => 'This ASN/OEN is already in use.',
        ];
    }
}
