<?php

namespace App\Http\Controllers;

use App\Events\LoginHistoryEvent;
use App\Mail\{
    VerificationCode
};
use App\Models\Guardian;
use App\Models\LoginAttempt;
use App\Models\PasswordResetToken;
use App\Models\Role;
use App\Models\SchoolYear;
use App\Models\User;
use App\Models\Organization;
use App\Models\Student;
use App\Traits\ApiResponse;
// use Illuminate\Auth\Events\PasswordReset;
use App\Traits\UserAuthority;
use Browser;
use Carbon\Carbon;
use Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Notifications\PasswordExpiredNotification;
use DB;

class AuthController extends Controller
{
    use ApiResponse, UserAuthority;

    protected $all_allowed_modules;

    protected $user_ip_address;

    protected $device_name;

    /** this is okay file */
    public function __construct()
    {
        $this->user_ip_address = \Request::ip();
        $this->middleware('auth:api', ['except' => ['login', 'forgot_password', 'reset_password', 'verifyUserDevice', 'setCodeForUser']]);
    }

    /**
     * Get a JWT token via given credentials.
     *
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $credentials = request(['email', 'password']);
        // check if parent, then check access
        $parent = request(['app_id']);
        if ($parent['app_id'] == 2) {
            if (!$this->parentLogin($credentials['email'])) {
                return $this->loginFailed('You are not authorized.');
            }

            if (!$this->checkStudentStatus($credentials['email'])) {
                return $this->loginFailed(config('constants.NO_ACTIVE_ENROLLMENT_OF_STUDENT'));
            }
        } else {
            if (!$this->adminLogin($credentials['email'])) {
                return $this->loginFailed('You are not authorized.');
            }
        }

        if (!$token = auth()->attempt($credentials)) {
            $logSaved = $this->loginAttempts('fail', 0);
            if (!$logSaved) {
                return $this->loginFailed('Invalid email or password.');
            }
            // count login Attempts
            $loginAttempts = $this->countLoginAttempts($credentials);
            if ($loginAttempts > 0) {
                return $this->loginFailed('Remaining Login Attempts - ' . $loginAttempts);
            } else {
                return $this->loginFailed('Your Account is Locked.');
            }
        }

        $user = User::where('email', $credentials['email'])->first();
        // check if User Exit but not Active
        if (!$user?->is_active) {
            return $this->loginFailed('Your Account is Locked.');
        }
        $this->checkPasswordExpiryNotification($user);
        /**check Is Device Registered */
        $checkDevice = 1; //$this->checkDevice($credentials);

        if ($checkDevice == 0) {
            /** add code into User Table for verification */
            $this->setCodeForUser();

            return response()->json(['code' => true]);
        }



        return $this->respondWithToken($token, $request['app_id']);
    }

    public function me()
    {
        return response()->json($this->guard()->user());
    }

    public function logout()
    {
        $this->guard()->logout();

        return response()->json(['message' => 'Successfully logged out']);
    }

    public function refresh(Request $request)
    {
        if (isset($request->id)) {
            $user = auth()->user();
            $user->organization_id = $request->id;
            $user->save();
        }

        return $this->respondWithToken($this->guard()->refresh(), $request['app_id']);
    }

    public function forgot_password(Request $request)
    {
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return ['title' => '', 'message' => 'We have e-mailed your password reset link', 'data' => ['email' => $request->email]];
        }
        $token = Str::random(64);
        PasswordResetToken::create([
            'email' => $request['email'],
            'token' => $token,
            'created_at' => Carbon::now(),
        ]);

        Mail::send(
            'emails.password-reset-email',
            [
                'name' => $user->name,
                'hostLink' => $request->hostLink,
                'email' => $request->email,
                'token' => $token,
                'org_name' => Organization::find($user->organization_id)?->value('org_name')
            ],
            function ($message) use ($request) {
                $message->to($request->email);
                $message->subject('Reset Password');
            }
        );

        return ['title' => '', 'message' => 'We have e-mailed your password reset link', 'data' => ['email' => $request->email]];

        //    -----------------------------------------
        $input = $request->all();
        $rules = [
            'email' => 'required|email',
        ];

        $customMessages = [
            'email.required' => 'Email is required.',
            'email.email' => 'Email Format is incorrect.',
        ];

        $validator = Validator::make($request->all(), $rules, $customMessages);
        if ($validator->fails()) {
            return ['title' => '', 'message' => 'Forgot Password action Failed', 'data' => ''];
        } else {

            try {
                $response = Password::sendResetLink($request->only('email'));

                switch ($response) {

                    case Password::RESET_LINK_SENT:
                        return ['title' => '', 'message' => trans($response), 'data' => ''];

                    case Password::INVALID_USER:
                        return ['title' => '', 'message' => trans($response), 'data' => ''];
                }
            } catch (\Swift_TransportException $ex) {
                return ['title' => '', 'message' => $ex->getMessage(), 'data' => ''];
            } catch (\Exception $ex) {
                return ['title' => '', 'message' => $ex->getMessage(), 'data' => ''];
            }

            return ['title' => '', 'message' => 'Reset Password here', 'data' => ''];
        }
    }

    public function reset_password(Request $request)
    {
        $updatePassword = PasswordResetToken::where([
            'email' => $request->email,
            'token' => $request->token,
        ])
            ->first();

        if (!$updatePassword) {
            return ['title' => '', 'message' => 'Invalid email or token', 'data' => '', 'status' => false];
        }

        $user = User::where('email', $request->email)
            ->update(['password' => Hash::make($request->password)]);

        PasswordResetToken::where(['email' => $request->email])->delete();

        return ['title' => '', 'message' => 'Your Password has been changed.', 'data' => '', 'status' => true];
    }

    protected function respondWithToken($token, $app_id, $impersonate = false)
    {

        $token = JWTAuth::fromUser($this->guard()->user());
        $user = Auth::user(); // Retrieve the authenticated user
        $this->loginAttempts('success', auth()->user()->id);
        $user->organizations;
        $user->organization;
        $user->teacherData;
        $user->role_ids = $user->roles->pluck('id');
        $user->themeSetting;
        $user->schoolYear;

        $roles_name = $user->roles->pluck('name');
        $all_data = $this->build_menu_for_user_according_to_roles_and_permissions($user, $app_id, $roles_name);

        $defaultRoute = 'default';
        if ($app_id == 1) {
            if ($roles_name->contains('Teacher')) {
                $defaultRoute = 'manage-class';
            } elseif ($roles_name->contains('Home Room Teacher')) {
                $defaultRoute = 'manage-class';
            } elseif ($roles_name->contains('Accountant')) {
                $defaultRoute = 'invoices';
            } else {
                $explodeRoute = explode('/', $all_data['protectedRoutes'][0]['path']);
                if (count($explodeRoute) > 1 && $explodeRoute[1] != '') {
                    $defaultRoute = $explodeRoute[1];
                } else {
                    $explodeRoute2 = explode('/', $all_data['protectedRoutes'][2]['path']);
                    $defaultRoute = $explodeRoute2[1];
                }
                // $defaultRoute = 'default';
            }
        } else {
            if ($roles_name->contains('Student')) {
                $defaultRoute = 'leave-application';
            } else {
                $defaultRoute = 'student-dashboard';
            }
        }


        return response()->json([
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => $this->guard()->factory()->getTTL() * 60,
            'user' => $user,
            'userRole' => $user->roles->pluck('name'),
            'dashBoardNavTree' => $all_data['dashBoardNavTree'],
            'protectedRoutes' =>  $all_data['protectedRoutes'],
            'user_all_informations' => $all_data['allowedModules'],
            'defaultRoute' => $defaultRoute,
            'impersonate' => $impersonate,
            'defaultYear' => $user->schoolYear,
        ]);
    }

    /**
     * @var
     * loginAsUser
     */
    public function loginAsUser()
    {
        $lastUserId = Auth::user()->id;
        /** logout the Existing User */
        $this->guard()->logout();
        $data = request();

        $user = User::find($data['id']);
        if ($user) {
            $token = JWTAuth::fromUser($user);
            /**  Log in the new user with the new token  */
            Auth::login($user);

            return $this->respondWithToken($token, $data['app_id'], $lastUserId);
        }

        return $this->loginFailed('Some thing went wrong!');
    }

    public function loginAsGuardian()
    {
        $data = request();

        $user = User::find($data['id']);
        if ($user) {
            $token = JWTAuth::fromUser($user);
            /**  Log in the new user with the new token  */
            Auth::login($user);

            return $this->respondWithToken($token, $data['app_id']);
        }

        return $this->loginFailed('Some thing went wrong!');
    }

    public function leaveImpersonateUser()
    {
        /** logout the Existing User */
        $this->guard()->logout();
        $data = request();

        $user = User::find($data['id']);
        if ($user) {
            $token = JWTAuth::fromUser($user);
            /**  Log in the new user with the new token    */
            Auth::login($user);

            return $this->respondWithToken($token, $data['app_id']);
        }

        return $this->loginFailed('Some thing went wrong!');
    }

    public function guard()
    {
        return Auth::guard();
    }

    private function loginHistory($user)
    {
        Event::dispatch(new LoginHistoryEvent($user));
    }

    // first check if Parent have access to login
    public function parentLogin($email)
    {
        $user = User::with('roles')->where('email', $email)->first();
        $userRoles = $user->roles->pluck('name');
        if (!$user) {
            return false;
        }
        if ($userRoles->contains('Student')) {
            $result = Student::whereUserId($user->id)->first();
            if ($result) {
                return true;
            }
        } elseif ($userRoles->contains('Guardian')) {
            $result = Guardian::with(['getSiblingStudents'])->whereUserId($user->id)->first();
            if ($result) {
                foreach ($result->students as $key => $value) {
                    if ($value->pivot->allow_portal_access) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public function checkStudentStatus($email)
    {
        $user = User::with('roles')->where('email', $email)->first();
        $userRoles = $user->roles->pluck('name');
        if (!$user) {
            return false;
        }

        if ($userRoles->contains('Student')) {
            $result = Student::whereUserId($user->id)->first();
            if ($result) {
                return true;
            }
        } elseif ($userRoles->contains('Guardian')) {
            $result = Guardian::with(['getSiblingStudents.activeEnrollments'])->whereUserId($user->id)->first();

            if ($result) {
                foreach ($result->getSiblingStudents as $student) {
                    if (isset($student->activeEnrollments)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }


    public function adminLogin($email)
    {
        $user = User::where('email', $email)->first();
        if (!$user) {
            return false;
        }
        // $result = User::with(['roles'])
        //     ->whereHas('roles', function ($query) {
        //         $query->where(function ($query) {
        //             $query->where('name', Role::SUPER_ADMIN)
        //                 ->orWhere('name', Role::ADMIN);
        //         })->orWhere('name', Role::TEACHER);
        //     })
        //     ->where('id', $user->id)
        //     ->first();

        $result = User::with(['roles'])
            ->whereHas('roles', function ($query) {
                $query->whereNotIn('name', [Role::GUARDIAN]);
            })
            ->where('id', $user->id)
            ->first();

        if ($result) {
            return true;
        }

        return false;
    }

    public function loginAttempts($status, $user_id)
    {
        if (isset($status) && $status != 'success') {
            $chec_user = User::where('email', '=', request('email'))->first();
            if (isset($chec_user)) {
                $user_id = $chec_user->id;
            } else {
                return false;
            }
        }
        if (Browser::isMobile()) {
            $this->device_name = 'Mobile';
        }
        if (Browser::isTablet()) {
            $this->device_name = 'Tablet';
        }
        if (Browser::isDesktop()) {
            $this->device_name = 'Desktop';
        }

        // $currentUserInfo = \Location::get($ip);
        LoginAttempt::create(
            [
                'login_attempted_with' => request('email'),
                'user_id' => $user_id,
                'password' => $status == 'success' ? '' : request('password'),
                'device_name' => $this->device_name,
                'login_status' => $status == 'success' ? 1 : 0,
                'device_family' => Browser::deviceFamily(),
                'device_model' => Browser::deviceModel(),
                'os_name' => Browser::platformName(),
                'browser_name' => Browser::browserName(),
                'ip_address' => $this->user_ip_address,
                'zip_code' => '--',
                'country' => '--',
                'state' => '--',
                'longitude' => '--',
                'latitude' => '--',
            ]
        );

        return true;
    }

    //count Login Attempts
    public function countLoginAttempts($data)
    {
        $email = $data['email'];
        $today = Carbon::today()->toDateString();

        $attempts = LoginAttempt::where(['login_attempted_with' => $email, 'login_status' => 0])->whereDate('created_at', $today);
        $totalWrong = $attempts->count();
        //: TODO set wrong attempts from ENV or CONSTANT
        if ($totalWrong >= config('constants.LOGIN_ATTEMPTS')) {
            User::where(['email' => request('email')])->update(['is_active' => 0]);
            $totalWrong = config('constants.LOGIN_ATTEMPTS');
        }

        return config('constants.LOGIN_ATTEMPTS') - $totalWrong;
    }

    /** check if this Device is Already in use */
    public function checkDevice($data)
    {
        $email = $data['email'];

        return LoginAttempt::where([
            'login_attempted_with' => $email,
            'login_status' => 1,
            'os_name' => Browser::platformName(),
            'browser_name' => Browser::browserName(),
        ])->count();
    }

    /** set User Verification Code */
    public function setCodeForUser()
    {
        $credentials = request(['email', 'password']);
        if (!$token = auth()->attempt($credentials)) {
        }

        $code = auth()->user()->generateCode();

        // \Log::info(auth()->user());
        $userCode = User::where('id', auth()->user()->id)->update(['remember_token' => $code]);
        if ($userCode) {
            $user = User::find(auth()->user()->id);
            $emailData = [
                'name' => $user->full_name,
                'code' => $code,
            ];
            Mail::to($user->email)->send(new VerificationCode($emailData));
        }
    }

    /** verify User Device */
    public function verifyUserDevice(Request $request)
    {
        $data = $request->all();
        $credentials = request(['email', 'password']);
        /** Login with Creds */
        if (!$token = auth()->attempt($credentials)) {
            return $this->generalError('Invalid verification code.');
        }

        if (isset($data['code'])) {
            $user = User::where(['remember_token' => $data['code']])->first();
            if ($user) {
                $user->remember_token = null;
                $user->save();

                return $this->respondWithToken($token, $request['app_id']);
            }
        }

        return $this->generalError('Invalid verification code.');
    }

    public function checkPasswordExpiryNotification($user){
        $pwdExpiryDays = DB::table('system_settings')
            ->where('name', 'password_expiry')
            ->value('value');

        if (!is_numeric($pwdExpiryDays) || (int)$pwdExpiryDays <= 0) {
            return;
        }

        $days = (int)$pwdExpiryDays;

        if (empty($user->password_changed_at)) {
            return;
        }

        $changedDaysAgo = Carbon::parse($user->password_changed_at)->diffInDays(now());

        if ($changedDaysAgo >= $days) {
            $latestNotification = $user->notifications()
                ->where('type', PasswordExpiredNotification::class)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($latestNotification) {
                $lastNotifiedDaysAgo = Carbon::parse($latestNotification->created_at)->diffInDays(now());
                if ($lastNotifiedDaysAgo >= $days) {
                    $user->notify(new PasswordExpiredNotification());
                }
            } else {
                $user->notify(new PasswordExpiredNotification());
            }
        }
    }
}
