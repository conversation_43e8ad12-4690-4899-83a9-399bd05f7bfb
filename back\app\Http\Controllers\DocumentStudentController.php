<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDocumentStudentRequest;
use App\Http\Requests\UpdateDocumentStudentRequest;
use App\Models\DocumentStudent;
use App\Repositories\Interfaces\DocumentStudentRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class DocumentStudentController extends Controller
{
    use ApiErrorResponse;

    private $DocumentStudentRepositoryInterface;

    public function __construct(DocumentStudentRepositoryInterface $DocumentStudentRepositoryInterface)
    {
        $this->DocumentStudentRepositoryInterface = $DocumentStudentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->DocumentStudentRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDocumentStudentRequest $request)
    {
        $state = $this->DocumentStudentRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($DocumentStudent)
    {
        $state = $this->DocumentStudentRepositoryInterface->show($DocumentStudent);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDocumentStudentRequest $request, DocumentStudent $DocumentStudent)
    {
        $state = $this->DocumentStudentRepositoryInterface->update($request, $DocumentStudent);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($DocumentStudent)
    {
        $state = $this->DocumentStudentRepositoryInterface->destroy($DocumentStudent);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getStudentDocuments(Request $requestData)
    {
        $state = $this->DocumentStudentRepositoryInterface->getStudentDocuments($requestData);
        return response()->json($state, Response::HTTP_OK);
    }
}
