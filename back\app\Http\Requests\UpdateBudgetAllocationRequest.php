<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateBudgetAllocationRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'name' => [
                        'required',
                        'max:255',
                        $Id ? Rule::unique('budget_allocations')->ignore($Id) : 'unique:budget_allocations',
                    ],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The budget allocation name is required.',
                    'name.max' => 'The budget allocation name must not exceed 255 characters.',
                    'name.unique' => 'The budget allocation name is already taken. Please choose a different name.',
                ];
            }
        
}