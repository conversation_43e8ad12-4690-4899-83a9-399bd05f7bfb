<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LeaveAllocation;
use App\Http\Requests\StoreLeaveAllocationRequest;
use App\Http\Requests\UpdateLeaveAllocationRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\LeaveAllocationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;

class LeaveAllocationController extends Controller
{
    use ApiErrorResponse;

    private $LeaveAllocationRepositoryInterface;

    
            public function __construct(LeaveAllocationRepositoryInterface $LeaveAllocationRepositoryInterface)
            {
                $this->LeaveAllocationRepositoryInterface = $LeaveAllocationRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->LeaveAllocationRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreLeaveAllocationRequest $request)
            {
                try{
                    DB::beginTransaction();
                    $state = $this->LeaveAllocationRepositoryInterface->store($request);
                    DB::commit();
                    return $this->successResponse($state, config('constants.DATA_INSERTED'));

                }catch(\Exception $e){
                    DB::rollBack();
                    return $this->generalError($e->getMessage());
                }

            
            }

            /**
             * Display the specified resource.
             */
            public function show($LeaveAllocation)
            {
                $state = $this->LeaveAllocationRepositoryInterface->show($LeaveAllocation);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateLeaveAllocationRequest $request, LeaveAllocation $LeaveAllocation)
            {
                $state = $this->LeaveAllocationRepositoryInterface->update($request, $LeaveAllocation);
                if ($state) {
                    return $this->successResponse($state, config('constants.DATA_UPDATED'));
                } else {
                    return $this->generalError(config('constants.GENERAL_ERROR'));
                }
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($LeaveAllocation)
            {
                $state = $this->LeaveAllocationRepositoryInterface->destroy($LeaveAllocation);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}