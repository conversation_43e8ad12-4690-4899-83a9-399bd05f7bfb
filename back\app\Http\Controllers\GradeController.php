<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreGradeRequest;
use App\Http\Requests\UpdateGradeRequest;
use App\Models\Grade;
use App\Repositories\Interfaces\GradeRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Response;

class GradeController extends Controller
{
    use ApiResponse;

    private $GradeRepositoryInterface;

    public function __construct(GradeRepositoryInterface $GradeRepositoryInterface)
    {
        $this->GradeRepositoryInterface = $GradeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->GradeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreGradeRequest $request)
    {
        $state = $this->GradeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Grade)
    {
        $state = $this->GradeRepositoryInterface->show($Grade);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateGradeRequest $request, Grade $Grade)
    {
        $state = $this->GradeRepositoryInterface->update($request, $Grade);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Grade)
    {
        $state = $this->GradeRepositoryInterface->destroy($Grade);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
