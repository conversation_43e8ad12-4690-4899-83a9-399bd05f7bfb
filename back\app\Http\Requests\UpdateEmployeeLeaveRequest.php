<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeLeaveRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'description' => ['sometimes', 'string', 'nullable'],
                    'reason' => ['sometimes', 'string', 'nullable'],
                    'from' => ['sometimes', 'date', 'nullable'],
                    'to' => ['sometimes', 'date', 'nullable', 'after_or_equal:from'],
                    'leave_type_id' => ['sometimes', 'integer', 'nullable', 'exists:leave_types,id'],
                    'status' => ['sometimes', 'string', 'nullable', Rule::in(['approved', 'pending', 'rejected', 'cancelled', 'cancel pending'])],
                    'documents' => ['sometimes', 'file', 'nullable', 'mimes:pdf,doc,docx,jpg,jpeg,png', 'max:2048'],
                    'id' => ['sometimes', 'integer', 'exists:employee_leaves,id']
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The employee leave name is required.',
                    'name.max' => 'The employee leave name must not exceed 255 characters.',
                    'name.unique' => 'The employee leave name is already taken. Please choose a different name.',
                ];
            }
        
}