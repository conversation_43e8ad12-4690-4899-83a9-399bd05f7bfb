<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchoolCategoryRequest;
use App\Http\Requests\UpdateSchoolCategoryRequest;
use App\Models\SchoolCategory;
use App\Repositories\Interfaces\SchoolCategoryRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SchoolCategoryController extends Controller
{
    use ApiErrorResponse;

    private $SchoolCategoryRepositoryInterface;

    public function __construct(SchoolCategoryRepositoryInterface $SchoolCategoryRepositoryInterface)
    {
        $this->SchoolCategoryRepositoryInterface = $SchoolCategoryRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SchoolCategoryRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolCategoryRequest $request)
    {
        $state = $this->SchoolCategoryRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SchoolCategory)
    {
        $state = $this->SchoolCategoryRepositoryInterface->show($SchoolCategory);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolCategoryRequest $request, SchoolCategory $SchoolCategory)
    {
        $state = $this->SchoolCategoryRepositoryInterface->update($request, $SchoolCategory);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SchoolCategory)
    {
        $state = $this->SchoolCategoryRepositoryInterface->destroy($SchoolCategory);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
