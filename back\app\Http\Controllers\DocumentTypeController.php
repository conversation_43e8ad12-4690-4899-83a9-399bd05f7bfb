<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DocumentType;
use App\Http\Requests\StoreDocumentTypeRequest;
use App\Http\Requests\UpdateDocumentTypeRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\DocumentTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;

class DocumentTypeController extends Controller
{
    use ApiErrorResponse;

    private $DocumentTypeRepositoryInterface;

    
            public function __construct(DocumentTypeRepositoryInterface $DocumentTypeRepositoryInterface)
            {
                $this->DocumentTypeRepositoryInterface = $DocumentTypeRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->DocumentTypeRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreDocumentTypeRequest $request)
            {
                $state = $this->DocumentTypeRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($DocumentType)
            {
                $state = $this->DocumentTypeRepositoryInterface->show($DocumentType);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateDocumentTypeRequest $request, DocumentType $DocumentType)
            {
                $state = $this->DocumentTypeRepositoryInterface->update($request, $DocumentType);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($DocumentType)
            {
                $state = $this->DocumentTypeRepositoryInterface->destroy($DocumentType);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            public function allDocumentCategories()
            {
                $state = $this->DocumentTypeRepositoryInterface->allDocumentCategories();
                return response()->json($state, Response::HTTP_OK);
            }
            
}