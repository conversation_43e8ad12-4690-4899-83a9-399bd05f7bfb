<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class GenerateObserverWithMethods extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-observer-with-methods {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $model = $this->argument('name');
        $observerClassName = "{$model}Observer";

        $eventMethods = [
            'creating', 'created', 'updating', 'updated',
            'deleting', 'deleted', 'restoring', 'restored',
        ];

        $observerContent = "<?php\n\nnamespace App\Observers;\n\n";
        $observerContent .= "use App\Models\\$model;\n\n";
        $observerContent .= "class $observerClassName\n";
        $observerContent .= "{\n";

        foreach ($eventMethods as $eventMethod) {
            $observerContent .= $this->generateEventHandlerMethod($eventMethod, $model);
        }

        $observerContent .= "}\n";

        $filesystem = new Filesystem();
        $path = app_path("Observers/$observerClassName.php");

        if (! $filesystem->exists($path)) {
            $filesystem->put($path, $observerContent);
            $this->info("$observerClassName created successfully.");
        } else {
            $this->error("$observerClassName already exists.");
        }
    }

    private function generateEventHandlerMethod($eventMethod, $model)
    {
        return <<<EOT
        /**
         * Handle the model "$eventMethod" event.
         *
         * @param  \App\Models\Model  \$model
         * @return void
         */
        public function $eventMethod({$model} \$model)
        {
            // Implement your logic for the "$eventMethod" event here
        }

        EOT;
    }
}
