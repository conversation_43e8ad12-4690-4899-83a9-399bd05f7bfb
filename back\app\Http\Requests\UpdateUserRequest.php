<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');        
        return [
            'first_name' => 'required',
            'last_name' => 'required',
            'cell_number' => 'required',
            'email' => 'required|unique:users,email,'. $Id,//'required|email|', $Id ? Rule::unique('users')->ignore($Id) : 'unique:users',
            // 'password' => 'required',
            'is_internal' => 'required',
            'roles' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'The first name is required.',
            'last_name.required' => 'The last name is required.',
            'cell_number.required' => 'The phone is required.',
            'email.required' => 'The email is required.',
            // 'password.required' => 'The password is required.',
            'is_internal.required' => 'Internal is required.',
            'roles.required' => 'Role is required.',
        ];
    }
}
