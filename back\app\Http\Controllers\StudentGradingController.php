<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Repositories\Interfaces\StudentGradingRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Response;


class StudentGradingController extends Controller
{
    use ApiResponse;
    private $StudentGradingRepositoryInterface;
    public function __construct(StudentGradingRepositoryInterface $StudentGradingRepositoryInterface)
    {
        $this->StudentGradingRepositoryInterface = $StudentGradingRepositoryInterface;
    }

    public function getSchoolTerms(Request $request){
        $result['data'] = $this->StudentGradingRepositoryInterface->getSchoolTerms($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function preLoadGradingData(Request $request){
        $result['data'] = $this->StudentGradingRepositoryInterface->preLoadGradingData($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function getClassStudentsForGrades(Request $request)
    {
        $result['data'] = $this->StudentGradingRepositoryInterface->getClassStudentsForGrades($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function viewStudentGradeDetail(Request $request)
    {
        $result['data'] = $this->StudentGradingRepositoryInterface->viewStudentGradeDetail($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function getSingleStudentGraphData(Request $request)
    {
        $result['data'] = $this->StudentGradingRepositoryInterface->getSingleStudentGraphData($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function singleSubjectDetail(Request $request)
    {
        $result['data'] = $this->StudentGradingRepositoryInterface->singleSubjectDetail($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function parentSingleStudentGradeDetail(Request $request)
    {
        $result['data'] = $this->StudentGradingRepositoryInterface->parentSingleStudentGradeDetail($request);
        return response()->json($result, Response::HTTP_OK);
    }
}
