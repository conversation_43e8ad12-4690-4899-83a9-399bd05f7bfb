<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchoolRoomRequest;
use App\Http\Requests\UpdateSchoolRoomRequest;
use App\Models\SchoolRoom;
use App\Repositories\Interfaces\SchoolRoomRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SchoolRoomController extends Controller
{
    use ApiErrorResponse;

    private $SchoolRoomRepositoryInterface;

    public function __construct(SchoolRoomRepositoryInterface $SchoolRoomRepositoryInterface)
    {
        $this->SchoolRoomRepositoryInterface = $SchoolRoomRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SchoolRoomRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolRoomRequest $request)
    {
        $state = $this->SchoolRoomRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SchoolRoom)
    {
        $state = $this->SchoolRoomRepositoryInterface->show($SchoolRoom);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolRoomRequest $request, SchoolRoom $SchoolRoom)
    {
        $state = $this->SchoolRoomRepositoryInterface->update($request, $SchoolRoom);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SchoolRoom)
    {
        $state = $this->SchoolRoomRepositoryInterface->destroy($SchoolRoom);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
