<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SystemLogs;
use App\Http\Requests\StoreSystemLogsRequest;
use App\Http\Requests\UpdateSystemLogsRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\SystemLogsRepositoryInterface;
use App\Traits\ApiErrorResponse;

class SystemLogsController extends Controller
{
    use ApiErrorResponse;

    private $SystemLogsRepositoryInterface;
    public function __construct(SystemLogsRepositoryInterface $SystemLogsRepositoryInterface)
    {
        $this->SystemLogsRepositoryInterface = $SystemLogsRepositoryInterface;
    }
    public function getLogs(Request $request)
    {        
        $result['data'] = $this->SystemLogsRepositoryInterface->getLogs($request);
        return response()->json($result, Response::HTTP_OK);
    }
}