<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMaritalStatusRequest;
use App\Http\Requests\UpdateMaritalStatusRequest;
use App\Models\MaritalStatus;
use App\Repositories\Interfaces\MaritalStatusRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class MaritalStatusController extends Controller
{
    use ApiErrorResponse;

    private $MaritalStatusRepositoryInterface;

    public function __construct(MaritalStatusRepositoryInterface $MaritalStatusRepositoryInterface)
    {
        $this->MaritalStatusRepositoryInterface = $MaritalStatusRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->MaritalStatusRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMaritalStatusRequest $request)
    {
        $state = $this->MaritalStatusRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($MaritalStatus)
    {
        $state = $this->MaritalStatusRepositoryInterface->show($MaritalStatus);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMaritalStatusRequest $request, MaritalStatus $MaritalStatus)
    {
        $state = $this->MaritalStatusRepositoryInterface->update($request, $MaritalStatus);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($MaritalStatus)
    {
        $state = $this->MaritalStatusRepositoryInterface->destroy($MaritalStatus);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
