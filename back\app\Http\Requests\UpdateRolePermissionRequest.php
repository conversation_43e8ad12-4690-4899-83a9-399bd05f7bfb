<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRolePermissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'required',
            'role_id' => 'required',
            'modulePermissions' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'organization_id.required' => 'The Organization is required.',
            'role_id.required' => 'The Role is required.',
            'modulePermissions.required' => 'The Permission is required.',
        ];
    }
}
