<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EmployeeLeave;
use App\Http\Requests\StoreEmployeeLeaveRequest;
use App\Http\Requests\UpdateEmployeeLeaveRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\EmployeeLeaveRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\LeaveAllocation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmployeeLeaveController extends Controller
{
    use ApiErrorResponse;

    private $EmployeeLeaveRepositoryInterface;

    
            public function __construct(EmployeeLeaveRepositoryInterface $EmployeeLeaveRepositoryInterface)
            {
                $this->EmployeeLeaveRepositoryInterface = $EmployeeLeaveRepositoryInterface;
            }
        
    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                try{
                    $state = $this->EmployeeLeaveRepositoryInterface->index();
                    return response()->json($state, Response::HTTP_OK);
                }catch(\Exception $e){
                    return $this->generalError($e->getMessage());
                }
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreEmployeeLeaveRequest $request)
            {
                try{
                    DB::beginTransaction();

                $state = $this->EmployeeLeaveRepositoryInterface->store($request);
                DB::commit();
                return $this->successResponse($state, config('constants.DATA_INSERTED'));
                }catch(\Exception $e){
                    DB::rollback();
                    LOG::error($e->getMessage());
                    return $this->generalError($e->getMessage());
                }
                
            }

            /**
             * Display the specified resource.
             */
            public function show($EmployeeLeave)
            {
                $state = $this->EmployeeLeaveRepositoryInterface->show($EmployeeLeave);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateEmployeeLeaveRequest $request)
            {
                try{
                    $EmployeeLeave = $request->id;
                }catch(\Exception $e){
                    return $this->generalError($e->getMessage());
                }

                $state = $this->EmployeeLeaveRepositoryInterface->update($request, $EmployeeLeave);
                if($state) {
                    return $this->successResponse($state, config('constants.DATA_UPDATED'));
                } else {
                    return $this->generalError(config('constants.GENERAL_ERROR'));
                }
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($EmployeeLeave)
            {
                $state = $this->EmployeeLeaveRepositoryInterface->destroy($EmployeeLeave);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
            public function getLeaveBalance()
            {
                try{
                    $state = $this->EmployeeLeaveRepositoryInterface->getLeaveBalance();
                    return response()->json($state, Response::HTTP_OK);
                }catch(\Exception $e){
                    return $this->generalError($e->getMessage());

                }
          

            }
            public function getUpcomingLeaves(Request $request)
            {
                $state = $this->EmployeeLeaveRepositoryInterface->getUpcomingLeaves($request);
                return response()->json($state, Response::HTTP_OK);
            }

            public function getEmployeeLeaves()
            {
                try{
                    $state = $this->EmployeeLeaveRepositoryInterface->employeeLeaves();
                    return response()->json($state, Response::HTTP_OK);
                }catch(\Exception $e){
                    return $this->generalError($e->getMessage());
                }
                
            }

            public function generateExcel(Request $request)
            {
                $state = $this->EmployeeLeaveRepositoryInterface->generateExcel($request);
                return response()->json($state, Response::HTTP_OK);
            }
}