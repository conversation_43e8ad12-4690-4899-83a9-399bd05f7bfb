<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePersonalInformationRequest;
use App\Http\Requests\UpdatePersonalInformationRequest;
use App\Models\PersonalInformation;
use App\Repositories\Interfaces\PersonalInformationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class PersonalInformationController extends Controller
{
    use ApiErrorResponse;

    private $PersonalInformationRepositoryInterface;

    public function __construct(PersonalInformationRepositoryInterface $PersonalInformationRepositoryInterface)
    {
        $this->PersonalInformationRepositoryInterface = $PersonalInformationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->PersonalInformationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePersonalInformationRequest $request)
    {
        $state = $this->PersonalInformationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($PersonalInformation)
    {
        $state = $this->PersonalInformationRepositoryInterface->show($PersonalInformation);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePersonalInformationRequest $request, PersonalInformation $PersonalInformation)
    {
        $state = $this->PersonalInformationRepositoryInterface->update($request, $PersonalInformation);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($PersonalInformation)
    {
        $state = $this->PersonalInformationRepositoryInterface->destroy($PersonalInformation);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
