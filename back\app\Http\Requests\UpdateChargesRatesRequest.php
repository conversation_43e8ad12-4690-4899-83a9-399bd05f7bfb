<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateChargesRatesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            // 'school_year_id' => 'required',
            // 'organization_grade_level_id' => 'required',
            'charge_type_id' => 'required',
            'amount' => 'required|numeric',
            'effective_from' => 'required|date',
            'effective_to' => 'required|date|after_or_equal:effective_from',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // 'school_year_id.required' => 'The school year is required.',
            // 'organization_grade_level_id.required' => 'The organization grade level is required.',
            'charge_type_id.required' => 'The charge type is required.',
            'amount.required' => 'The amount is required.',
            'amount.numeric' => 'The amount must be a numeric value.',
            'effective_from.required' => 'The effective from date is required.',
            'effective_from.date' => 'The effective from date must be a valid date.',
            'effective_to.required' => 'The effective to date is required.',
            'effective_to.date' => 'The effective to date must be a valid date.',
            'effective_to.after_or_equal' => 'The effective to date must be equal to or after the effective from date.',
        ];
    }
}
