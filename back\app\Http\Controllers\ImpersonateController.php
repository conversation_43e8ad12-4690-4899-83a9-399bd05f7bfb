<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Lab404\Impersonate\Services\ImpersonateManager;

class ImpersonateController extends Controller
{
    public function startImpersonate(Request $request, ImpersonateManager $impersonateManager)
    {
        // Validate and authorize user (e.g., admin) to perform impersonation
        $this->authorize('impersonate', User::class);

        $userId = $request->input('userId');
        $userToImpersonate = User::findOrFail($userId);

        $impersonateManager->impersonate($userToImpersonate);

        return response()->json(['message' => 'Impersonation started']);
    }

    public function endImpersonate(ImpersonateManager $impersonateManager)
    {
        $impersonateManager->leave();

        return response()->json(['message' => 'Impersonation ended']);
    }
}
