<?php

namespace App\Http\Requests\Assignment;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssignmentTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $id = $this->get('id');

        return [
            'name' => ['required', 'min:3',
                $id ? Rule::unique('assignment_types')->ignore($id) : 'unique:assignment_types',
            ],
        ];
    }

    /**some validation messages  */
    public function messages()
    {
        //TODO: languag translate messages
        return [
            'name.required' => 'The name field is required.',
            'name.min' => 'The name must be at least :min characters.',
            'name.unique' => 'The name has already been taken.',
        ];
    }
}
