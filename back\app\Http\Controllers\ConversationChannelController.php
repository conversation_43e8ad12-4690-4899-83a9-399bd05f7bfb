<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreConversationChannelRequest;
use App\Http\Requests\UpdateConversationChannelRequest;
use App\Models\ConversationChannel;
use App\Repositories\Interfaces\ConversationChannelRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ConversationChannelController extends Controller
{
    use ApiErrorResponse;

    private $ConversationChannelRepositoryInterface;

    public function __construct(ConversationChannelRepositoryInterface $ConversationChannelRepositoryInterface)
    {
        $this->ConversationChannelRepositoryInterface = $ConversationChannelRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ConversationChannelRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreConversationChannelRequest $request)
    {
        $state = $this->ConversationChannelRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ConversationChannel)
    {
        $state = $this->ConversationChannelRepositoryInterface->show($ConversationChannel);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateConversationChannelRequest $request, ConversationChannel $ConversationChannel)
    {
        $state = $this->ConversationChannelRepositoryInterface->update($request, $ConversationChannel);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ConversationChannel)
    {
        $state = $this->ConversationChannelRepositoryInterface->destroy($ConversationChannel);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
