<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMachineRequest;
use App\Http\Requests\UpdateMachineRequest;
use App\Models\Machine;
use App\Repositories\Interfaces\MachineRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class MachineController extends Controller
{
    use ApiErrorResponse;

    private $MachineRepositoryInterface;

    public function __construct(MachineRepositoryInterface $MachineRepositoryInterface)
    {
        $this->MachineRepositoryInterface = $MachineRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->MachineRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMachineRequest $request)
    {
        $state = $this->MachineRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Machine)
    {
        $state = $this->MachineRepositoryInterface->show($Machine);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMachineRequest $request, Machine $Machine)
    {
        $state = $this->MachineRepositoryInterface->update($request, $Machine);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Machine)
    {
        $state = $this->MachineRepositoryInterface->destroy($Machine);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function generateUniqueCode(Request $request){
        $state = $this->MachineRepositoryInterface->generateUniqueCode($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function verifyUniqueId(Request $requestData){

        if (!isset($requestData['security_token']) || $requestData['security_token'] != "8877655") {
            return response()->json("Invalid application you are using", Response::HTTP_UNAUTHORIZED);
        }

        return [
            "success" => Machine::where('mac_address', $requestData->mac_address)
                ->where('status', 0)->exists()
        ];
    }

}
