<?php

namespace App\Http\Controllers;

use App\Repositories\Interfaces\StudentGradeBookRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class StudentGradeBookController extends Controller
{
    use ApiResponse;

    protected $StudentGradeBookRepositoryInterface;

    public function __construct(StudentGradeBookRepositoryInterface $StudentGradeBookRepositoryInterface)
    {
        $this->StudentGradeBookRepositoryInterface = $StudentGradeBookRepositoryInterface;
    }
    public function getClassStudentsForGrades(Request $request)
    {
        $result['data'] = $this->StudentGradeBookRepositoryInterface->getClassStudentsForGrades($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function addCourseGradeCategory(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentGradeBookRepositoryInterface->addCourseGradeCategory($request);
            DB::commit();
            return $this->successResponse($result, 'Course grade category added!');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function addStudentObtainMarks(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentGradeBookRepositoryInterface->addStudentObtainMarks($request);
            DB::commit();
            return $this->successResponse($result, 'Marks saved!');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function finalizedCourseGradeCategory(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentGradeBookRepositoryInterface->finalizedCourseGradeCategory($request);
            DB::commit();
            return $this->successResponse($result, 'Marks finalized!');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function viewStudentGradeDetail(Request $request)
    {
        $result['data'] = $this->StudentGradeBookRepositoryInterface->viewStudentGradeDetail($request);
        return response()->json($result, Response::HTTP_OK);
    }
}
