<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePaymentModeRequest;
use App\Http\Requests\UpdatePaymentModeRequest;
use App\Models\PaymentMode;
use App\Repositories\Interfaces\PaymentModeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class PaymentModeController extends Controller
{
    use ApiErrorResponse;

    private $PaymentModeRepositoryInterface;

    public function __construct(PaymentModeRepositoryInterface $PaymentModeRepositoryInterface)
    {
        $this->PaymentModeRepositoryInterface = $PaymentModeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->PaymentModeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePaymentModeRequest $request)
    {
        $state = $this->PaymentModeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($PaymentMode)
    {
        $state = $this->PaymentModeRepositoryInterface->show($PaymentMode);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePaymentModeRequest $request, PaymentMode $PaymentMode)
    {
        $state = $this->PaymentModeRepositoryInterface->update($request, $PaymentMode);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($PaymentMode)
    {
        $state = $this->PaymentModeRepositoryInterface->destroy($PaymentMode);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
