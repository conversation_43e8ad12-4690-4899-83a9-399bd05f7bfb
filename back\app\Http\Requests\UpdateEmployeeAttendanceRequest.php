<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeAttendanceRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'employee_id' => [
                        'required',
                    ],
                    'att_date_time' => [
                        'required',
                    ],
                    'status' => [
                        'required',
                    ],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'employee_id.required' => 'The employee_id name is required.',
                    'att_date_time.required' => 'The att_date_time name is required.',
                    'status.required' => 'The status name is required.',
                ];
            }
        
}