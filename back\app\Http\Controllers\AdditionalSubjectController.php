<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponse;
use Illuminate\Support\Str;
use App\Traits\ReportCardTrait;
use App\Models\AdditionalSubject;
use App\Models\StudentReportCard;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreAdditionalSubjectRequest;
use App\Http\Requests\UpdateAdditionalSubjectRequest;


class AdditionalSubjectController extends Controller
{
    use ApiResponse, ReportCardTrait;
    /**generalSuccess
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    // public function store(StoreAdditionalSubjectRequest $request)
    // {
    //     try {
    //         DB::beginTransaction();        
    //         $data = $request->all();
    //         $studentReportCardId = $request->student_report_card_id;
    //         if (!$studentReportCardId) {
    //             throw new \Exception('Something went wrong');
    //         }

    //         $grouped = [];

    //         foreach ($data as $key => $value) {
    //             if (!Str::contains($key, '_term')) {
    //                 continue;
    //             }

    //             // Match format like Listening_Proficiency_Level_term1
    //             preg_match('/^(.*)_term([1-3])$/', $key, $matches);
    //             if ($matches) {
    //                 $titleKey = $matches[1];
    //                 $termIndex = $matches[2];

    //                 if (!isset($grouped[$titleKey])) {
    //                     $grouped[$titleKey] = ['term1' => null, 'term2' => null, 'term3' => null];
    //                 }

    //                 $grouped[$titleKey]["term{$termIndex}"] = $value;
    //             }
    //         }

    //         foreach ($grouped as $key => $terms) {
    //             $termData = collect($terms)->filter(fn($v) => !is_null($v))->toArray();
            
    //             $updateData = array_merge($termData, [            
    //                 'category' => Str::contains($key, 'Proficiency_Level') ? 'Group_A' : 'Group_B',
    //                 'user_id' => auth()->user()->id,
    //             ]);
            
    //             AdditionalSubject::updateOrCreate(
    //                 [
    //                     'student_report_card_id' => $studentReportCardId,
    //                     'title' => $key, // Keep underscores intact
    //                 ],
    //                 $updateData
    //             );
    //         }    

    //         $studentReportCard = StudentReportCard::with(['additionalSubjects.user'])->find($studentReportCardId);
    //         $result = $this->additionalSubjects($studentReportCard);
    //         DB::commit();
    //         return $this->successResponse($result, config('constants.DATA_UPDATED'));
    //     } catch (\Throwable $th) {            
    //         DB::rollBack();
    //         return $this->generalError(config('constants.GENERAL_ERROR'));
    //     }
    // }

    public function store(StoreAdditionalSubjectRequest $request)
{
    try {
        DB::beginTransaction();

        $studentReportCardId = $request->student_report_card_id;

        if (!$studentReportCardId) {
            throw new \Exception('Student Report Card ID is required.');
        }

        $grouped = [];

        foreach ($request->all() as $key => $value) {
            if (!Str::endsWith($key, '_term')) {
                continue;
            }

            // Example key: Listening_Proficiency_Level_term
            $title = Str::replaceLast('_term', '', $key);

            if ($value !== null && $value !== '') {
                $grouped[$title] = [
                    'title' => $title,
                    'term' => $value,
                    'category' => Str::contains($title, 'Proficiency_Level') ? 'Group_A' : 'Group_B',
                    'user_id' => auth()->id(),
                ];
            }
        }

        foreach ($grouped as $data) {
            AdditionalSubject::updateOrCreate(
                [
                    'student_report_card_id' => $studentReportCardId,
                    'title' => $data['title'],
                ],
                [
                    'term' => $data['term'],
                    'category' => $data['category'],
                    'user_id' => $data['user_id'],
                ]
            );
        }

        $studentReportCard = StudentReportCard::with(['additionalSubjects.user'])->findOrFail($studentReportCardId);
        $result = $this->additionalSubjects($studentReportCard);

        DB::commit();
        return $this->successResponse($result, config('constants.DATA_UPDATED'));

    } catch (\Throwable $th) {
        DB::rollBack();
        return $this->generalError(config('constants.GENERAL_ERROR'));
    }
}



    /**
     * Display the specified resource.
     */
    public function show(AdditionalSubject $additionalSubject)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAdditionalSubjectRequest $request, AdditionalSubject $additionalSubject)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AdditionalSubject $additionalSubject)
    {
        //
    }
}
