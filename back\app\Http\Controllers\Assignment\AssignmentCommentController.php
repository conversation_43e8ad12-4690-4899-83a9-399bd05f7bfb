<?php

namespace App\Http\Controllers\Assignment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Assignment\StoreAssignmentCommentRequest;
use App\Http\Requests\Assignment\UpdateAssignmentCommentRequest;
use App\Models\Assignment\AssignmentComment;

class AssignmentCommentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAssignmentCommentRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAssignmentCommentRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(AssignmentComment $assignmentComment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(AssignmentComment $assignmentComment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAssignmentCommentRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateAssignmentCommentRequest $request, AssignmentComment $assignmentComment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(AssignmentComment $assignmentComment)
    {
        //
    }
}
