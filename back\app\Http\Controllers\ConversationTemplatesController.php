<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreConversationTemplatesRequest;
use App\Http\Requests\UpdateConversationTemplatesRequest;
use App\Models\ConversationTemplates;
use App\Repositories\Interfaces\ConversationTemplatesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ConversationTemplatesController extends Controller
{
    use ApiErrorResponse;

    private $ConversationTemplatesRepositoryInterface;

    public function __construct(ConversationTemplatesRepositoryInterface $ConversationTemplatesRepositoryInterface)
    {
        $this->ConversationTemplatesRepositoryInterface = $ConversationTemplatesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ConversationTemplatesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreConversationTemplatesRequest $request)
    {
        $state = $this->ConversationTemplatesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ConversationTemplates)
    {
        $state = $this->ConversationTemplatesRepositoryInterface->show($ConversationTemplates);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateConversationTemplatesRequest $request, ConversationTemplates $ConversationTemplates)
    {
        $state = $this->ConversationTemplatesRepositoryInterface->update($request, $ConversationTemplates);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ConversationTemplates)
    {
        $state = $this->ConversationTemplatesRepositoryInterface->destroy($ConversationTemplates);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
