<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAttendanceModificationRequest;
use App\Http\Requests\UpdateAttendanceModificationRequest;
use App\Models\AttendanceModification;
use App\Repositories\Interfaces\AttendanceModificationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class AttendanceModificationController extends Controller
{
    use ApiErrorResponse;

    private $AttendanceModificationRepositoryInterface;

    public function __construct(AttendanceModificationRepositoryInterface $AttendanceModificationRepositoryInterface)
    {
        $this->AttendanceModificationRepositoryInterface = $AttendanceModificationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->AttendanceModificationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAttendanceModificationRequest $request)
    {
        $state = $this->AttendanceModificationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($AttendanceModification)
    {
        $state = $this->AttendanceModificationRepositoryInterface->show($AttendanceModification);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAttendanceModificationRequest $request, AttendanceModification $AttendanceModification)
    {
        $state = $this->AttendanceModificationRepositoryInterface->update($request, $AttendanceModification);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($AttendanceModification)
    {
        $state = $this->AttendanceModificationRepositoryInterface->destroy($AttendanceModification);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
