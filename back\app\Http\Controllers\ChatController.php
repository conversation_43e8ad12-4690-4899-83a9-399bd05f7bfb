<?php

namespace App\Http\Controllers;

use App\Models\Chat;
use App\Models\Message;
use App\Events\TypingEvent;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\StoreChatRequest;
use App\Http\Requests\UpdateChatRequest;
use App\Http\Requests\MessageChatRequest;
use App\Repositories\Interfaces\ChatRepositoryInterface;

class ChatController extends Controller
{
    use ApiErrorResponse;

    private $ChatRepositoryInterface;

    public function __construct(ChatRepositoryInterface $ChatRepositoryInterface)
    {
        $this->ChatRepositoryInterface = $ChatRepositoryInterface;
    }

    public function getStudentTeachers(Request $request)
    {
        $result = $this->ChatRepositoryInterface->getStudentTeachers($request->all());
        return response()->json($result, Response::HTTP_OK);
    }

    public function createConversation(Request $request)
    {
        $result = $this->ChatRepositoryInterface->createConversation($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function createGroupConversationByTeacher(StoreChatRequest $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ChatRepositoryInterface->createGroupConversationByTeacher($request);
            DB::commit();
            return $this->successResponse($result, 'Group conversation created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return $this->generalError(config('constants.INTERNAL_SERVER_ERROR'));
        }
    }

    public function getUserConversations(Request $request)
    {
        $result = $this->ChatRepositoryInterface->getUserConversations($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function getConversationMessages(Request $request)
    {
        $result = $this->ChatRepositoryInterface->getConversationMessages($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function sendMessage(MessageChatRequest $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->ChatRepositoryInterface->sendMessage($request);
            DB::commit();
            return response()->json($result, Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->generalError($e->getMessage());
        }
    }

    public function markReadMessages(Request $request)
    {
        $result = $this->ChatRepositoryInterface->markReadMessages($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function totalUnreadMessages(Request $request)
    {
        $result = $this->ChatRepositoryInterface->totalUnreadMessages($request);
        return response()->json($result, Response::HTTP_OK);
    }
}
