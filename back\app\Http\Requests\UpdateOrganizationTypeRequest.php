<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateOrganizationTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'org_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('organization_types')->ignore($Id) : 'unique:organization_types',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'org_type.required' => 'The organization type name is required.',
            'org_type.max' => 'The organization type name must not exceed 255 characters.',
            'org_type.unique' => 'The organization type name is already taken. Please choose a different name.',
        ];
    }
}
