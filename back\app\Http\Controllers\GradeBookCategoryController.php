<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreGradeBookCategoryRequest;
use App\Http\Requests\UpdateGradeBookCategoryRequest;
use App\Models\GradeBookCategory;
use App\Repositories\Interfaces\GradeBookCategoryRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class GradeBookCategoryController extends Controller
{
    use ApiErrorResponse;

    private $GradeBookCategoryRepositoryInterface;

    public function __construct(GradeBookCategoryRepositoryInterface $GradeBookCategoryRepositoryInterface)
    {
        $this->GradeBookCategoryRepositoryInterface = $GradeBookCategoryRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->GradeBookCategoryRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreGradeBookCategoryRequest $request)
    {
        $state = $this->GradeBookCategoryRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($GradeBookCategory)
    {
        $state = $this->GradeBookCategoryRepositoryInterface->show($GradeBookCategory);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateGradeBookCategoryRequest $request, GradeBookCategory $GradeBookCategory)
    {
        $state = $this->GradeBookCategoryRepositoryInterface->update($request, $GradeBookCategory);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($GradeBookCategory)
    {
        $state = $this->GradeBookCategoryRepositoryInterface->destroy($GradeBookCategory);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
