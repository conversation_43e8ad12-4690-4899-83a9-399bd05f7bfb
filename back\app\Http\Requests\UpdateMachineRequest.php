<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateMachineRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        return [
            'name' => [
                'required',
                'max:255',
                $Id ? Rule::unique('machines')->ignore($Id) : 'unique:machines',
            ],
            'mac_address' => [
                'required',
                'max:255',
                $Id ? Rule::unique('machines')->ignore($Id) : 'unique:machines',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The machine name is required.',
            'name.max' => 'The machine name must not exceed 255 characters.',
            'name.unique' => 'The machine name is already taken. Please choose a different name.',
            'mac_address.required' => 'The MAC address is required.',
            'mac_address.max' => 'The MAC address must not exceed 255 characters.',
            'mac_address.unique' => 'The MAC address is already taken. Please choose a different mac address.',
        ];
    }
}
