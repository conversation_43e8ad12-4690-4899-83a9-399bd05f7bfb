<?php

namespace App\Http\Requests\Assignment;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTopicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $id = $this->get('id');
        $organizationGradeLevelId = $this->get('organization_grade_level_id');
        $courseId = $this->get('course_id');

        return [
            'name' => ['required', 'min:3',
                    Rule::unique('topics')
                    ->where(function ($query) use ($organizationGradeLevelId, $courseId) {
                        return $query->where('organization_grade_level_id', $organizationGradeLevelId)
                                    ->where('course_id', $courseId);
                    })
                    ->ignore($id),
            ],
            'organization_grade_level_id' => 'required',
            'course_id' => 'required',
        ];
    }

    /**some validation messages  */
    public function messages()
    {
        //TODO: languag translate messages
        return [
            'name.required' => 'The name field is required.',
            'name.min' => 'The name must be at least :min characters.',
            // 'name.unique' => 'The name has already been taken.',
            'organization_grade_level_id.required' => 'The organization grade level field is required.',
            'course_id.required' => 'The course field is required.',
        ];
    }
}
