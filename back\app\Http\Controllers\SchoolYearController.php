<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchoolYearRequest;
use App\Http\Requests\UpdateSchoolYearRequest;
use App\Models\SchoolYear;
use App\Repositories\Interfaces\SchoolYearRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class SchoolYearController extends Controller
{
    use ApiErrorResponse;

    private $SchoolYearRepositoryInterface;

    public function __construct(SchoolYearRepositoryInterface $SchoolYearRepositoryInterface)
    {
        $this->SchoolYearRepositoryInterface = $SchoolYearRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SchoolYearRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolYearRequest $request)
    {
        $state = $this->SchoolYearRepositoryInterface->store($request);

        return $state ? $this->successResponse($state, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SchoolYear)
    {
        $state = $this->SchoolYearRepositoryInterface->show($SchoolYear);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolYearRequest $request, SchoolYear $SchoolYear)
    {
        $state = $this->SchoolYearRepositoryInterface->update($request, $SchoolYear);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SchoolYear)
    {
        $state = $this->SchoolYearRepositoryInterface->destroy($SchoolYear);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function setDefaultYear(Request $SchoolYear)
    {
        try {
            DB::beginTransaction();
            $data = $this->SchoolYearRepositoryInterface->setDefaultYear($SchoolYear);
            DB::commit();

            return $this->successResponse($data, config('constants.DEFAULT_YEAR'));
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function setUserDefaultYear(Request $SchoolYear)
    {
        try {
            DB::beginTransaction();
            $data = $this->SchoolYearRepositoryInterface->setUserDefaultYear($SchoolYear);
            DB::commit();

            return $this->successResponse($data, config('constants.DEFAULT_YEAR'));
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
