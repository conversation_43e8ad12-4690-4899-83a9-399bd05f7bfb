<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCalendarEventRequest;
use App\Http\Requests\UpdateCalendarEventRequest;
use App\Models\CalendarEvent;
use App\Repositories\Interfaces\CalendarEventRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class CalendarEventController extends Controller
{
    use ApiErrorResponse;

    private $CalendarEventRepositoryInterface;

    public function __construct(CalendarEventRepositoryInterface $CalendarEventRepositoryInterface)
    {
        $this->CalendarEventRepositoryInterface = $CalendarEventRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CalendarEventRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCalendarEventRequest $request)
    {
        $state = $this->CalendarEventRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     */
    public function show($CalendarEvent)
    {
        $state = $this->CalendarEventRepositoryInterface->show($CalendarEvent);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCalendarEventRequest $request, CalendarEvent $CalendarEvent)
    {
        $state = $this->CalendarEventRepositoryInterface->update($request, $CalendarEvent);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($CalendarEvent)
    {
        $state = $this->CalendarEventRepositoryInterface->destroy($CalendarEvent);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
