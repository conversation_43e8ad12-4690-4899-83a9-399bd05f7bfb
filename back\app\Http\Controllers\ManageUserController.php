<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreManageUserRequest;
use App\Http\Requests\UpdateManageUserRequest;
use App\Models\ManageUser;
use App\Repositories\Interfaces\ManageUserRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ManageUserController extends Controller
{
    use ApiErrorResponse;

    private $ManageUserRepositoryInterface;

    public function __construct(ManageUserRepositoryInterface $ManageUserRepositoryInterface)
    {
        $this->ManageUserRepositoryInterface = $ManageUserRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ManageUserRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    public function filter(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->filter($request);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreManageUserRequest $request)
    {

        $state = $this->ManageUserRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(ManageUser $ManageUser)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateManageUserRequest $request, ManageUser $ManageUser)
    {
        $state = $this->ManageUserRepositoryInterface->update($request, $ManageUser);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ManageUser)
    {
        $state = $this->ManageUserRepositoryInterface->destroy($ManageUser);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function countData(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->countData($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getEnrollmentData(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->getEnrollmentData($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function clearNotificationData(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->clearNotificationData($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getMonthlyAttendance(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->getMonthlyAttendance($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getNotificationData(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->getNotificationData($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentBirthdays(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->getStudentBirthdays($request);

        return response()->json($state, Response::HTTP_OK);
    }

    public function getCalendarEvents(Request $request)
    {
        $state = $this->ManageUserRepositoryInterface->getCalendarEvents($request);

        return response()->json($state, Response::HTTP_OK);
    }
}
