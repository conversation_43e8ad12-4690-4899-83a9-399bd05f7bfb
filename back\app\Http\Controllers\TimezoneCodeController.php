<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTimezoneCodeRequest;
use App\Http\Requests\UpdateTimezoneCodeRequest;
use App\Models\TimezoneCode;
use App\Repositories\Interfaces\TimezoneCodeRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class TimezoneCodeController extends Controller
{
    use ApiErrorResponse;

    private $TimezoneCodeRepositoryInterface;

    public function __construct(TimezoneCodeRepositoryInterface $TimezoneCodeRepositoryInterface)
    {
        $this->TimezoneCodeRepositoryInterface = $TimezoneCodeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TimezoneCodeRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTimezoneCodeRequest $request)
    {
        $state = $this->TimezoneCodeRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($TimezoneCode)
    {
        $state = $this->TimezoneCodeRepositoryInterface->show($TimezoneCode);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTimezoneCodeRequest $request, TimezoneCode $TimezoneCode)
    {
        $state = $this->TimezoneCodeRepositoryInterface->update($request, $TimezoneCode);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TimezoneCode)
    {
        $state = $this->TimezoneCodeRepositoryInterface->destroy($TimezoneCode);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
