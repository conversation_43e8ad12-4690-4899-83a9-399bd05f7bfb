<?php

namespace App\Http\Controllers\Assignment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Assignment\StoreAssignmentTypeRequest;
use App\Http\Requests\Assignment\UpdateAssignmentTypeRequest;
use App\Models\Assignment\AssignmentType;
use App\Repositories\Interfaces\Assignment\AssignmentTypeRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AssignmentTypeController extends Controller
{
    use ApiResponse;

    private $AssignmentTypeRepositoryInterface;

    public function __construct(AssignmentTypeRepositoryInterface $AssignmentTypeRepositoryInterface)
    {
        $this->AssignmentTypeRepositoryInterface = $AssignmentTypeRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $result = $this->AssignmentTypeRepositoryInterface->index($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAssignmentTypeRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreAssignmentTypeRequest $request)
    {
        $result = $this->AssignmentTypeRepositoryInterface->store($request);

        return $result ?
            $this->generalSuccess(config('constants.DATA_INSERTED'))
            :
            $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AssignmentType  $assignmentType
     * @return \Illuminate\Http\Response
     */
    public function show(AssignmentType $assignmentType)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AssignmentType  $assignmentType
     * @return \Illuminate\Http\Response
     */
    public function edit(AssignmentType $assignmentType)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAssignmentTypeRequest  $request
     * @param  \App\Models\AssignmentType  $assignmentType
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateAssignmentTypeRequest $request, AssignmentType $assignmentType)
    {
        $result = $this->AssignmentTypeRepositoryInterface->update($request, $assignmentType);

        return $result ?
            $this->generalSuccess(config('constants.DATA_UPDATED'))
            :
            $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AssignmentType  $assignmentType
     * @return \Illuminate\Http\Response
     */
    public function destroy(AssignmentType $assignmentType)
    {
        $state = $this->AssignmentTypeRepositoryInterface->destroy($assignmentType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
