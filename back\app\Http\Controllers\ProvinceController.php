<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProvinceRequest;
use App\Http\Requests\UpdateProvinceRequest;
use App\Models\Province;
use App\Repositories\Interfaces\ProvinceRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ProvinceController extends Controller
{
    use ApiErrorResponse;

    private $ProvinceRepositoryInterface;

    public function __construct(ProvinceRepositoryInterface $ProvinceRepositoryInterface)
    {
        $this->ProvinceRepositoryInterface = $ProvinceRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ProvinceRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProvinceRequest $request)
    {
        $state = $this->ProvinceRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Province)
    {
        $state = $this->ProvinceRepositoryInterface->show($Province);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProvinceRequest $request, Province $Province)
    {
        $state = $this->ProvinceRepositoryInterface->update($request, $Province);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Province)
    {
        $state = $this->ProvinceRepositoryInterface->destroy($Province);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
