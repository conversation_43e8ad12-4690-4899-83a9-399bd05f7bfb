<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateLeaveApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'id' => 'required|exists:leave_requests,id',
            'class_student_ids' => 'required|array',
            'class_student_ids.*' => 'integer|exists:class_students,id',
            'date' => 'required',
            'absent_reason_id' => 'required',
            'status' => 'required|in:0',
            'attendance_code' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'id.required' => 'Something went wrong.',
            'class_student_ids.required' => 'The student is required.',
            'date.required' => 'The date is required.',    
            'absent_reason_id.required' => 'The reason is required.', 
            'status.required' => 'The status is required.',
            'status.in' => 'Status is invalid.',
            'attendance_code.required' => 'The attendance code is required.',   
        ];
    }
}
