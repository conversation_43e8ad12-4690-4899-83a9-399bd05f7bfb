<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreEmployeeAttendanceRequest;
use App\Http\Requests\UpdateEmployeeAttendanceRequest;
use App\Models\EmployeeAttendance;
use App\Models\Machine;
use App\Models\User;
use App\Repositories\Interfaces\EmployeeAttendanceRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmployeeAttendanceController extends Controller
{
    use ApiErrorResponse;

    private $EmployeeAttendanceRepositoryInterface;

    public function __construct(EmployeeAttendanceRepositoryInterface $EmployeeAttendanceRepositoryInterface)
    {
        $this->EmployeeAttendanceRepositoryInterface = $EmployeeAttendanceRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->EmployeeAttendanceRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreEmployeeAttendanceRequest $request)
    {
        $state = $this->EmployeeAttendanceRepositoryInterface->store($request);
        if (isset($state) && $state === 1) {
            return $this->generalError('Already Checked In');
        }
        if (isset($state) && $state === 0) {
            return $this->generalError('Already Checked Out');
        }

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) :
        $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * Display the specified resource.
     */
    public function show($EmployeeAttendance)
    {
        $state = $this->EmployeeAttendanceRepositoryInterface->show($EmployeeAttendance);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEmployeeAttendanceRequest $request, EmployeeAttendance $EmployeeAttendance)
    {
        $state = $this->EmployeeAttendanceRepositoryInterface->update($request, $EmployeeAttendance);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($EmployeeAttendance)
    {
        $state = $this->EmployeeAttendanceRepositoryInterface->destroy($EmployeeAttendance);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    //
    public function markEmployeeAttendance(Request $request)
    {
        $requestData = $request->all();

        // Validate request data
        if (empty($requestData)) {
            return response()->json("Invalid data you are sending", Response::HTTP_BAD_REQUEST);
        }

        // Validate the security token
        if (!isset($requestData['security_token']) || $requestData['security_token'] != "8877655") {
            return response()->json("Invalid application you are using", Response::HTTP_UNAUTHORIZED);
        }

        if (!isset($requestData['pin'])) {
            return response()->json("Pin is required", Response::HTTP_UNAUTHORIZED);
        }

        // Get employee_id and mac_address from request
        $employeeId = $requestData['employee_id'];
        $macAddress = $requestData['mac_address'];

        // Fetch user and check if the machine is tagged to the user
        $user = User::where('employee_id', $employeeId)->first();

        if (!$user) {
            return response()->json("Employee not found", Response::HTTP_NOT_FOUND);
        }

        if (!isset($user->attendance_pin)) {
            return response()->json("Pin is not set", Response::HTTP_NOT_FOUND);
        }

        if ($requestData['pin'] != $user->attendance_pin) {
            return response()->json("Invalid Pin", Response::HTTP_NOT_FOUND);
        }

        // Check if the machine with this MAC address is tagged to the user
        $machine = Machine::where('mac_address', $macAddress)
            ->with(['wifiRouters'])
            ->first();

        if ($machine && $user->machines->contains($machine->id)) {

            if ($machine->wifiRouters->isEmpty()) {
                return response()->json("Wifi router not found to this machine", Response::HTTP_NOT_FOUND);
            }

            $wifiRouterMacAddresses = $machine->wifiRouters->pluck('mac_address')->toArray();

            if (!(isset($requestData['wifi_mac']) && in_array($requestData['wifi_mac'], $wifiRouterMacAddresses))) {
                return response()->json("Your are not allowed to use this wifi to mark attendance", Response::HTTP_BAD_REQUEST);
            }

            // Get current date
            $currentDate = Carbon::today();

            // Check if any attendance exists for today for this user
            $existingAttendance = EmployeeAttendance::where('user_id', $user->id)
                ->whereDate('created_at', $currentDate)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($existingAttendance && $existingAttendance->created_at) {
                if (Carbon::parse($existingAttendance->att_date_time)->diffInMinutes(Carbon::now()) < 5) {
                    return response()->json("You can mark your attendance again 5 minutes after the previous submission.", Response::HTTP_BAD_REQUEST);
                }
            }

            // Determine status based on existing records
            if ($existingAttendance) {
                // Toggle status based on the last record (1 -> 0, 0 -> 1)
                $nextStatus = $existingAttendance->status == 1 ? 0 : 1;
            } else {
                // If no attendance record exists, set status to 1
                $nextStatus = 1;
            }

            // Mark attendance with toggled or initial status
            EmployeeAttendance::create([
                'user_id' => $user->id,
                'machine_id' => $machine->id,
                'att_date_time' => Carbon::now()->format('Y-m-d H:i:s'),
                'status' => $nextStatus, // Toggle status or start with 1
            ]);

            return response()->json("Attendance marked successfully", Response::HTTP_OK);
        } else {
            return response()->json("Machine not tagged to this user", Response::HTTP_FORBIDDEN);
        }
    }

    public function getTodayEmployeeAttendance(Request $request)
    {
        $requestData = $request->all();

        if (!isset($requestData['security_token']) || $requestData['security_token'] != "8877655") {
            return response()->json("Invalid application you are using", Response::HTTP_UNAUTHORIZED);
        }

        if (!isset($requestData['pin'])) {
            return response()->json("Pin is required", Response::HTTP_UNAUTHORIZED);
        }

        if (!isset($requestData['employee_id'])) {
            return response()->json("Employee ID is required", Response::HTTP_UNAUTHORIZED);
        }

        $user = User::where('employee_id', $requestData['employee_id'])
            ->where('attendance_pin', $requestData['pin'])
            ->first();

        if ($user) {
            $attendance = EmployeeAttendance::where('user_id', $user->id)
                ->whereDate('created_at', Carbon::today())
                ->orderBy('created_at', 'desc')
                ->first();
            if ($attendance) {
                return response()->json(['attendance_status' => $attendance->status], 200);
            } else {
                return response()->json(['attendance_status' => 0], 200);
            }
        } else {
            return response()->json(['message' => 'User not found'], 401);
        }
        return response()->json("", Response::HTTP_UNAUTHORIZED);
    }

    public function generateExcel(Request $request){
        $result = $this->EmployeeAttendanceRepositoryInterface->generateExcel($request);
                return response()->json([
                    'file_url' => $result
                ], 200);
    }

}
