<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class UpdateManageRouteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            // 'organization_id' => 'required',
            'school_year_id' => 'required',
        ];

        // Check if it's an update request (e.g., if "id" is present in the request)
        if (request()->has('id')) {
            $id = request('id'); // Get the ID of the current record being updated
            // For updates, allow the current record's combination to exist
            $rules['route_name'] = [
                'required', 'max:200',
                function ($attribute, $value, $fail) use ($id) {

                    $exists = DB::table('manage_routes')
                        ->whereRaw('LOWER(route_name) = LOWER(?)', [$value])
                        ->where('organization_id', auth()->user()->organization->id)
                        ->where('id', '!=', $id) // Exclude the current record being updated
                        ->exists();

                    if ($exists) {
                        $fail('This route is already assigned to this organization.');
                    }
                },
            ];
        } else {
            // For new records, enforce the uniqueness of the combination
            $rules['route_name'] = [
                'required', 'max:200',
                function ($attribute, $value, $fail) {
                    // Check if there is a record with the same combination of student_id, bus_id, and transportation_mode
                    $exists = DB::table('manage_routes')
                        ->whereRaw('LOWER(route_name) = LOWER(?)', [$value])
                        ->where('organization_id', auth()->user()->organization->id)
                        ->exists();

                    if ($exists) {
                        $fail('This route is already assigned to this organization.');
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'route_name.required' => 'The manage routes name is required.',
            'route_name.max' => 'The manage routes name must not exceed 255 characters.',
        ];
    }
}
