<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StudentChargesSummary;
use App\Http\Requests\StoreStudentChargesSummaryRequest;
use App\Http\Requests\UpdateStudentChargesSummaryRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\StudentChargesSummaryRepositoryInterface;
use App\Traits\ApiErrorResponse;

class StudentChargesSummaryController extends Controller
{
    use ApiErrorResponse;

    private $StudentChargesSummaryRepositoryInterface;

    
            public function __construct(StudentChargesSummaryRepositoryInterface $StudentChargesSummaryRepositoryInterface)
            {
                $this->StudentChargesSummaryRepositoryInterface = $StudentChargesSummaryRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->StudentChargesSummaryRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreStudentChargesSummaryRequest $request)
            {
                $state = $this->StudentChargesSummaryRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($StudentChargesSummary)
            {
                $state = $this->StudentChargesSummaryRepositoryInterface->show($StudentChargesSummary);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateStudentChargesSummaryRequest $request, StudentChargesSummary $StudentChargesSummary)
            {
                $state = $this->StudentChargesSummaryRepositoryInterface->update($request, $StudentChargesSummary);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($StudentChargesSummary)
            {
                $state = $this->StudentChargesSummaryRepositoryInterface->destroy($StudentChargesSummary);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }

            public function generateExcel(Request $request) {
                $result = $this->StudentChargesSummaryRepositoryInterface->generateExcel($request);
                return $result;
            }
            
}