<?php

namespace App\Http\Controllers;

use DateTimeImmutable;
use App\Models\Student;
use App\Models\Attendance;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\AttendanceCode;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelReader;
use App\Repositories\Interfaces\AttendanceRepositoryInterface;

class AttendanceController extends Controller
{
    use ApiResponse;

    private $AttendanceRepositoryInterface;

    public function __construct(AttendanceRepositoryInterface $AttendanceRepositoryInterface)
    {
        $this->AttendanceRepositoryInterface = $AttendanceRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function getAttendanceLatestPolicy(Request $request)
    {
        $result['data'] = $this->AttendanceRepositoryInterface->getAttendanceLatestPolicy($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /**get attendance codes */
    public function getAttendanceCodes(Request $request)
    {
        $result['data'] = $this->AttendanceRepositoryInterface->getAttendanceCodes($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getClassStudentsForAtt(Request $request)
    {
        $result['data'] = $this->AttendanceRepositoryInterface->getClassStudentsForAtt($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * mark full class att
     */
    public function markAttendanceFullClass(Request $request)
    {
        $result = $this->AttendanceRepositoryInterface->markAttendanceFullClass($request);

        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * modify single att
     */
    public function modifySingleStudentAtt(Request $request)
    {
        $result = $this->AttendanceRepositoryInterface->modifySingleStudentAtt($request);

        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**
     * modify single att
     */
    public function markBulkStudentAtt(Request $request)
    {
        $result = $this->AttendanceRepositoryInterface->markBulkStudentAtt($request);

        return $result ? $this->generalSuccess(config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**get att for modi */
    public function getAttForModification(Request $request)
    {
        $result['data'] = $this->AttendanceRepositoryInterface->getAttForModification($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function saveBulkAttModification(Request $request)
    {
        $result = $this->AttendanceRepositoryInterface->saveBulkAttModification($request);

        return $result ? $this->generalSuccess(config('constants.DATA_UPDATED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function importAttendance(Request $request)
    {
        try {
            DB::beginTransaction();
            if ($request->hasFile('file')) {
                // $excelFileData = readExcelFile($request, 'file');
                $path = env('AWS_BUCKET_SCHOOL')  . '/imports/';
                $folderName      = 'imports/';
                $fileInfo = s3SingleFileUpload($path, $request, 'file', $folderName);
                // $path = Storage::path($fileInfo);
                // Create a SimpleExcelReader instance
                $reader = SimpleExcelReader::create($fileInfo)->preserveDateTimeFormatting()
                    ->getRows();
                $invalidStudents = [];

                $attendanceCodes = AttendanceCode::select('id', 'attendance_code', 'short_code')->get();

                foreach ($reader as $key => $value) {
                    $student = Student::where(['student_id' => $value['Student ID']])->first();
                    if (!$student) {
                        $value['error'] = 'Student not found';
                        $invalidStudents[] = $value;
                        continue;
                    }
                    $classStudent =  $student->classStudents()->orderBy('id', 'desc')->first();
                    if (!$classStudent) {
                        $value['error'] = 'Student Class not found';
                        $invalidStudents[] = $value;
                        continue;
                    }
                    $this->saveImportedAttendance($value, $classStudent, $attendanceCodes);
                }
            }
            DB::commit();
            return $this->successResponse($invalidStudents, 'Data imported successfully!');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    protected function saveImportedAttendance($data, $classStudent, $attendanceCodes)
    {
        if ($classStudent) {
            $attendanceRecords = [];
            foreach ($data as $date => $code) {
                if ($date !== "Student ID") {
                    $attendanceId = $this->filterAttendanceCodeId($code, $attendanceCodes);

                    Attendance::updateOrCreate(
                        [
                            'class_student_id' => $classStudent->id,
                            'attendance_date' => $date,
                        ],
                        [
                            'attendance_policy_id' => 1,
                            'attendance_code_id' => $attendanceId,
                            'created_by' => auth()->user()->id,
                            'updated_at' => now(),
                            'created_at' => now(),
                        ]
                    );

                    // $attendanceRecords[] = [
                    //     "attendance_policy_id" => 1,
                    //     "class_student_id" => $classStudent->id,
                    //     "attendance_date" => $date,
                    //     "attendance_code_id" => $attendanceId,
                    //     "created_by" => auth()->user()->id,
                    //     "created_at" => now(),
                    //     "updated_at" => now(),
                    // ];
                }
            }
            // Attendance::insert($attendanceRecords);
        }
    }
    protected function filterAttendanceCodeId($code, $attendanceCodes)
    {
        if ($code === "L") {
            $code = "LA";
        }
        $attendanceCode = $attendanceCodes->where('short_code', $code)->pluck('id')->first();
        return $attendanceCode ?? 5;
    }
}
