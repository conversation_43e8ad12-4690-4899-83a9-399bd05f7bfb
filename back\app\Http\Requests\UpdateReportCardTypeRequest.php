<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateReportCardTypeRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        return [
            'card_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('report_card_types')->ignore($Id) : 'unique:report_card_types',
            ],
            'organization_grade_level_ids' => 'required|array',
            'organization_grade_level_ids.*' => 'required|exists:organization_grade_level,id',
            'mark_type' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'card_type.required' => 'The report card type name is required.',
            'card_type.max' => 'The report card type name must not exceed 255 characters.',
            'card_type.unique' => 'The report card type name is already taken. Please choose a different name.',
            'organization_grade_level_ids.required' => 'The organization grade is required.',
            'organization_grade_level_ids.*.required' => 'The organization grade is required.',
            'mark_type.required' => 'The mark type is required.',
        ];
    }
}
