<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChargesSummary;
use App\Http\Requests\StoreChargesSummaryRequest;
use App\Http\Requests\UpdateChargesSummaryRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\ChargesSummaryRepositoryInterface;
use App\Traits\ApiErrorResponse;

class ChargesSummaryController extends Controller
{
    use ApiErrorResponse;

    private $ChargesSummaryRepositoryInterface;

    
            public function __construct(ChargesSummaryRepositoryInterface $ChargesSummaryRepositoryInterface)
            {
                $this->ChargesSummaryRepositoryInterface = $ChargesSummaryRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->ChargesSummaryRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreChargesSummaryRequest $request)
            {
                $state = $this->ChargesSummaryRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($ChargesSummary)
            {
                $state = $this->ChargesSummaryRepositoryInterface->show($ChargesSummary);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateChargesSummaryRequest $request, ChargesSummary $ChargesSummary)
            {
                $state = $this->ChargesSummaryRepositoryInterface->update($request, $ChargesSummary);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($ChargesSummary)
            {
                $state = $this->ChargesSummaryRepositoryInterface->destroy($ChargesSummary);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}