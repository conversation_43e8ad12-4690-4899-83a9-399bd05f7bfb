<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCommentRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        return [
            'comments' => 'required',
            'class_student_id' => 'required',
            'comment_category_id' => 'required',
            'manage_class_id' => 'required',
            'teacher_id' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'comments.required' => 'The comment is required.',
            'class_student_id.required' => 'The Student is required.',
            'comment_category_id.required' => 'The comment category is already taken.',
            'manage_class_id.required' => 'The class is required.',
            'teacher_id.required' => 'The teacher is required.',
        ];
    }

}
