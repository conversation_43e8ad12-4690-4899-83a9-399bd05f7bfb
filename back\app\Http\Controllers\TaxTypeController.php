<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TaxType;
use App\Http\Requests\StoreTaxTypeRequest;
use App\Http\Requests\UpdateTaxTypeRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\TaxTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;

class TaxTypeController extends Controller
{
    use ApiErrorResponse;

    private $TaxTypeRepositoryInterface;


    public function __construct(TaxTypeRepositoryInterface $TaxTypeRepositoryInterface)
    {
        $this->TaxTypeRepositoryInterface = $TaxTypeRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TaxTypeRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTaxTypeRequest $request)
    {
        $state = $this->TaxTypeRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($TaxType)
    {
        $state = $this->TaxTypeRepositoryInterface->show($TaxType);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTaxTypeRequest $request, TaxType $TaxType)
    {
        $state = $this->TaxTypeRepositoryInterface->update($request, $TaxType);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TaxType)
    {
        $state = $this->TaxTypeRepositoryInterface->destroy($TaxType);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
}
