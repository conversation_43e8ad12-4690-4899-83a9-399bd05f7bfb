<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateStudentCoursesRequest;
use App\Models\StudentCourses;
use App\Repositories\Interfaces\StudentCoursesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class StudentCoursesController extends Controller
{
    use ApiErrorResponse;

    private $StudentCoursesRepositoryInterface;

    public function __construct(StudentCoursesRepositoryInterface $StudentCoursesRepositoryInterface)
    {
        $this->StudentCoursesRepositoryInterface = $StudentCoursesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->StudentCoursesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentCoursePlan()
    {
        $state = $this->StudentCoursesRepositoryInterface->getStudentCoursePlan();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $state = $this->StudentCoursesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($StudentCourses)
    {
        $state = $this->StudentCoursesRepositoryInterface->show($StudentCourses);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentCoursesRequest $request, StudentCourses $StudentCourses)
    {
        $state = $this->StudentCoursesRepositoryInterface->update($request, $StudentCourses);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentCourses)
    {
        $state = $this->StudentCoursesRepositoryInterface->destroy($StudentCourses);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
