<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreChargesTypesRequest;
use App\Http\Requests\UpdateChargesTypesRequest;
use App\Models\ChargeType;
use App\Repositories\Interfaces\ChargesTypesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class ChargesTypesController extends Controller
{
    use ApiErrorResponse;

    private $ChargesTypesRepositoryInterface;

    public function __construct(ChargesTypesRepositoryInterface $ChargesTypesRepositoryInterface)
    {
        $this->ChargesTypesRepositoryInterface = $ChargesTypesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ChargesTypesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreChargesTypesRequest $request)
    {
        $state = $this->ChargesTypesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ChargesTypes)
    {
        $state = $this->ChargesTypesRepositoryInterface->show($ChargesTypes);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateChargesTypesRequest $request, ChargeType $ChargesTypes)
    {
        $state = $this->ChargesTypesRepositoryInterface->update($request, $ChargesTypes);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ChargesTypes)
    {
        $state = $this->ChargesTypesRepositoryInterface->destroy($ChargesTypes);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
