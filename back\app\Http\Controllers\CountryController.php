<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCountryRequest;
use App\Http\Requests\UpdateCountryRequest;
use App\Models\Country;
use App\Repositories\Interfaces\CountryRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class CountryController extends Controller
{
    use ApiErrorResponse;

    private $CountryRepositoryInterface;

    public function __construct(CountryRepositoryInterface $CountryRepositoryInterface)
    {
        $this->CountryRepositoryInterface = $CountryRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CountryRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCountryRequest $request)
    {
        $state = $this->CountryRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(Country $Country)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCountryRequest $request, Country $Country)
    {
        $state = $this->CountryRepositoryInterface->update($request, $Country);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Country)
    {
        $state = $this->CountryRepositoryInterface->destroy($Country);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
