<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateGradeBookCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'name' => [
                'required',
                'max:255',
                $Id ? Rule::unique('assignment_types')->ignore($Id) : 'unique:assignment_types',
            ],
            // 'is_final_grade' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The grade book category name is required.',
            'name.max' => 'The grade book category name must not exceed 255 characters.',
            'name.unique' => 'The grade book category name is already taken. Please choose a different name.',
            // 'is_final_grade.required' => 'The final grade field is required.',
        ];
    }
}
