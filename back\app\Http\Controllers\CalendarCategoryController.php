<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCalendarCategoryRequest;
use App\Http\Requests\UpdateCalendarCategoryRequest;
use App\Models\CalendarCategory;
use App\Repositories\Interfaces\CalendarCategoryRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class CalendarCategoryController extends Controller
{
    use ApiErrorResponse;

    private $CalendarCategoryRepositoryInterface;

    public function __construct(CalendarCategoryRepositoryInterface $CalendarCategoryRepositoryInterface)
    {
        $this->CalendarCategoryRepositoryInterface = $CalendarCategoryRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CalendarCategoryRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCalendarCategoryRequest $request)
    {
        $state = $this->CalendarCategoryRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($CalendarCategory)
    {
        $state = $this->CalendarCategoryRepositoryInterface->show($CalendarCategory);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCalendarCategoryRequest $request, CalendarCategory $CalendarCategory)
    {
        $state = $this->CalendarCategoryRepositoryInterface->update($request, $CalendarCategory);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($CalendarCategory)
    {
        $state = $this->CalendarCategoryRepositoryInterface->destroy($CalendarCategory);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
}
