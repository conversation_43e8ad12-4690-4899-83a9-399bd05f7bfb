<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProcurementItem;
use App\Http\Requests\StoreProcurementItemRequest;
use App\Http\Requests\UpdateProcurementItemRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\ProcurementItemRepositoryInterface;
use App\Traits\ApiErrorResponse;

class ProcurementItemController extends Controller
{
    use ApiErrorResponse;

    private $ProcurementItemRepositoryInterface;

    
            public function __construct(ProcurementItemRepositoryInterface $ProcurementItemRepositoryInterface)
            {
                $this->ProcurementItemRepositoryInterface = $ProcurementItemRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->ProcurementItemRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreProcurementItemRequest $request)
            {
                $state = $this->ProcurementItemRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($ProcurementItem)
            {
                $state = $this->ProcurementItemRepositoryInterface->show($ProcurementItem);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateProcurementItemRequest $request, ProcurementItem $ProcurementItem)
            {
                $state = $this->ProcurementItemRepositoryInterface->update($request, $ProcurementItem);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($ProcurementItem)
            {
                $state = $this->ProcurementItemRepositoryInterface->destroy($ProcurementItem);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}