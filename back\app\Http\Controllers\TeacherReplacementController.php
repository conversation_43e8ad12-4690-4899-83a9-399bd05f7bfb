<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TeacherReplacement;
use App\Http\Requests\StoreTeacherReplacementRequest;
use App\Http\Requests\UpdateTeacherReplacementRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\TeacherReplacementRepositoryInterface;
use App\Traits\ApiErrorResponse;

class TeacherReplacementController extends Controller
{
    use ApiErrorResponse;

    private $TeacherReplacementRepositoryInterface;


    public function __construct(TeacherReplacementRepositoryInterface $TeacherReplacementRepositoryInterface)
    {
        $this->TeacherReplacementRepositoryInterface = $TeacherReplacementRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TeacherReplacementRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $state = $this->TeacherReplacementRepositoryInterface->store($request->all());
        return $state ? $this->successResponse(false, config('constants.TEACHER_REPLACEMENT_MESSAGES')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($TeacherReplacement)
    {
        $state = $this->TeacherReplacementRepositoryInterface->show($TeacherReplacement);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTeacherReplacementRequest $request, TeacherReplacement $TeacherReplacement)
    {
        $state = $this->TeacherReplacementRepositoryInterface->update($request, $TeacherReplacement);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TeacherReplacement)
    {
        $state = $this->TeacherReplacementRepositoryInterface->destroy($TeacherReplacement);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }
}
