<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Traits\ApiErrorResponse;
use App\Models\StudentAttendance;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreStudentAttendanceRequest;
use App\Http\Requests\UpdateStudentAttendanceRequest;
use App\Repositories\Interfaces\StudentAttendanceRepositoryInterface;

class StudentAttendanceController extends Controller
{
    use ApiErrorResponse;

    private $StudentAttendanceRepositoryInterface;

    public function __construct(StudentAttendanceRepositoryInterface $StudentAttendanceRepositoryInterface)
    {
        $this->StudentAttendanceRepositoryInterface = $StudentAttendanceRepositoryInterface;
    }

    public function guardianStudentsAttendance(Request $request){
        try {
            $data['attendance'] = $this->StudentAttendanceRepositoryInterface->guardianStudentsAttendance($request);
            return response()->json($data, Response::HTTP_OK);
        } catch (\Exception $exp) {           
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }       
    }
    
    public function getAttendanceParentDashboard(Request $request)
    {
        $result['data'] = $this->StudentAttendanceRepositoryInterface->getAttendanceParentDashboard($request);
        return response()->json($result, Response::HTTP_OK);
    }
    // public function index()
    // {
    //     $state = $this->StudentAttendanceRepositoryInterface->index();

    //     return response()->json($state, Response::HTTP_OK);
    // }
    // public function store(StoreStudentAttendanceRequest $request)
    // {
    //     $state = $this->StudentAttendanceRepositoryInterface->store($request);

    //     return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    // }
    // public function show($StudentAttendance)
    // {
    //     $state = $this->StudentAttendanceRepositoryInterface->show($StudentAttendance);

    //     return response()->json($state, Response::HTTP_OK);
    // }
    // public function update(UpdateStudentAttendanceRequest $request, StudentAttendance $StudentAttendance)
    // {
    //     $state = $this->StudentAttendanceRepositoryInterface->update($request, $StudentAttendance);

    //     return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    // }
    // public function destroy($StudentAttendance)
    // {
    //     $state = $this->StudentAttendanceRepositoryInterface->destroy($StudentAttendance);
    //     if ($state != 1 && $state == 23000) {
    //         return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
    //     } else {
    //         return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
    //     }
    // }
}
