<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreInvoicesRequest;
use App\Http\Requests\UpdateInvoicesRequest;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Models\InvoicePaymentStudentCharge;
use App\Models\Payment;
use App\Models\StudentCharge;
use App\Models\StudentLedger;
use App\Repositories\Interfaces\InvoicesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoicesController extends Controller
{
    use ApiErrorResponse;

    private $InvoicesRepositoryInterface;

    public function __construct(InvoicesRepositoryInterface $InvoicesRepositoryInterface)
    {
        $this->InvoicesRepositoryInterface = $InvoicesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->InvoicesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreInvoicesRequest $request)
    {
        $checkFutureDatesStudentInvoices = $this->checkFutureDatesStudentInvoices($request);
        if (isset($checkFutureDatesStudentInvoices) && count($checkFutureDatesStudentInvoices) > 0) {
            return $this->generalWarning(config('constants.FUTURE_INVOICE_MESSAGES'), JsonResponse::HTTP_OK, $checkFutureDatesStudentInvoices);
        } else {
            $state = $this->InvoicesRepositoryInterface->store($request);
            if ($state['status']) {
                return $this->successResponse(false, config('constants.INVOICE_GEN'));
            } else {
                if (isset($state['roleBack'])) {
                    return $this->generalError(config('constants.GENERAL_ERROR'));
                } else {
                    return $this->generalWarning(config('constants.NOT_FOUND'));
                }
            }
        }
    }

    private function checkFutureDatesStudentInvoices($request)
    {
        $studentInvoicesData = [];
        if (isset($request->studentIds) && count($request->studentIds) > 0) {
            $studentInvoices = Invoice::with(['student', 'invoice_batch'])->whereIn('student_id', $request->studentIds)->whereDate('invoice_date', '>=', Carbon::parse($request->invoice_month)->format('Y-m-d'))->get();
            foreach ($studentInvoices as $studentId) {
                $studentInvoicesData[] = [
                    'batch_name' => $studentId->invoice_batch->batch_name,
                    'batch_status' => $studentId->invoice_batch->batch_status,
                    'student_name' => $studentId->student->full_name,
                    'student_id' => $studentId->student->student_id,
                    'std_id' => $studentId->student_id,
                    'invoice_date' => $studentId->invoice_date,
                    'invoice_no' => $studentId->invoice_no
                ];
            }
        }
        return $studentInvoicesData;
    }

    /**
     * Display the specified resource.
     */
    public function show($Invoices)
    {
        $state = $this->InvoicesRepositoryInterface->show($Invoices);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateInvoicesRequest $request, Invoice $Invoices)
    {
        $state = $this->InvoicesRepositoryInterface->update($request);
        if ($state['status']) {
            return $this->successResponse(false, config('constants.INVOICE_GEN'));
        } else {
            if (isset($state['roleBack'])) {
                return $this->generalError(config('constants.GENERAL_ERROR'));
            } else {
                return $this->generalWarning(config('constants.NOT_FOUND'));
            }
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Invoices)
    {
        $state = $this->InvoicesRepositoryInterface->destroy($Invoices);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function chargeTypeInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->chargeTypeInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function detailChargeTypeInvoices()
    {
        // $state = $this->InvoicesRepositoryInterface->detailChargeTypeInvoices();

        // return response()->json($state, Response::HTTP_OK);
    }

    public function monthlyWiseInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->monthlyWiseInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function detailMonthlyWiseInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->detailMonthlyWiseInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function studentWiseInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->studentWiseInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function detailStudentWiseInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->detailStudentWiseInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function annualInvoices()
    {
        // $state = $this->InvoicesRepositoryInterface->annualInvoices();

        // return response()->json($state, Response::HTTP_OK);
    }

    public function detailAnnualInvoices()
    {
        $state = $this->InvoicesRepositoryInterface->detailAnnualInvoices();

        return response()->json($state, Response::HTTP_OK);
    }

    public function invoicesRevenueSummary()
    {
        // $state = $this->InvoicesRepositoryInterface->invoicesRevenueSummary();
        // return response()->json($state, Response::HTTP_OK);
    }

    public function download_invoice(Request $request)
    {
        $data = $request->all();
        $invoice_file_name = $data['invoice_file_name'];

        return $fileUrl = asset('storage/app/public/InvoicePDF/' . $invoice_file_name);
    }

    public function invoiceDashboardData()
    {
        $state = $this->InvoicesRepositoryInterface->invoiceDashboardData();
        return response()->json($state, Response::HTTP_OK);
    }

    public function updateInvoiceAndPaymentData(Request $request)
    {
        try {
            DB::beginTransaction();

            $excludedIds = [
                5333,
                5334,
                5341,
                6633,
                6669,
                6696,
                6698,
                6711,
                6727,
                6731,
                6739,
                6741,
                6746,
                6752,
                6755,
                6770,
                6773,
                6775,
                6777
            ];

            // Get payments for student_id 21 that are not in invoice_payment and not excluded
            $payments = DB::table('payments')
                ->whereNotIn('id', function ($query) {
                    $query->select('payment_id')->from('invoice_payment');
                })
                // ->where('student_id', 191)
                ->whereNotIn('id', $excludedIds)
                ->orderBy('student_id', 'asc')
                ->get();

            // Format the payments (optional)
            $formattedPayments = $payments->map(function ($payment) {
                $payment->payment_date = Carbon::parse($payment->payment_date)->format('Y-m-d');
                $payment->created_at = Carbon::parse($payment->created_at)->format('Y-m-d');
                $payment->updated_at = Carbon::parse($payment->updated_at)->format('Y-m-d');
                $currentDate = Carbon::parse()->format('Y-m-d');

                $advance = true;
                $invoices_data = $this->get_student_pending_invoices($payment->student_id, $advance, false, $payment->organization_id);
                if ($invoices_data) {
                    $adv_data[] = $this->paymentProcess(
                        $payment,
                        $invoices_data,
                        $payment->id,
                        $currentDate,
                        $payment->receipt_number,
                        $advance,
                        $payment->organization_id,
                        $payment->student_id
                    );
                }
                return $payment;
            });

            DB::commit();
            return response()->json([
                'payments' => $formattedPayments,
            ]);
        } catch (\Exception $exp) {
            // Log the exception
            Log::error($exp);
            // Rollback the transaction
            DB::rollBack();

            return ['status' => false, 'roleBack' => 'Yes'];
        }
    }

    public function updateInvoiceAndPaymentUnappliedData(Request $request)
    {
        try {
            DB::beginTransaction();

            $excludedIds = [
                5333,
                5334,
                5341,
                6633,
                6669,
                6696,
                6698,
                6711,
                6727,
                6731,
                6739,
                6741,
                6746,
                6752,
                6755,
                6770,
                6773,
                6775,
                6777
            ];

            // Get payments for student_id 21 that are not in invoice_payment and not excluded
            $payments = DB::table('payments as p')
                ->leftJoin('invoice_payment as ip', 'p.id', '=', 'ip.payment_id')
                ->select('p.*', DB::raw('COALESCE(SUM(ip.payment_amount), 0) as total_applied_amount'))
                ->whereNotIn('p.id', $excludedIds)
                ->where('p.check_invoice_payment', 0)
                ->groupBy('p.id', 'p.payment_amount', 'p.applied_amount', 'p.student_id') // include all selected fields not using aggregates
                ->havingRaw('p.payment_amount > p.applied_amount')
                ->orderBy('p.student_id', 'asc')
                ->get();

            // Format the payments (optional)
            $formattedPayments = $payments->map(function ($payment) {
                $payment->payment_date = Carbon::parse($payment->payment_date)->format('Y-m-d');
                $payment->created_at = Carbon::parse($payment->created_at)->format('Y-m-d');
                $payment->updated_at = Carbon::parse($payment->updated_at)->format('Y-m-d');
                $currentDate = Carbon::parse()->format('Y-m-d');

                $advance = true;
                $invoices_data = $this->get_student_pending_invoices($payment->student_id, $advance, false, $payment->organization_id);
                if ($invoices_data) {
                    $adv_data[] = $this->paymentProcess(
                        $payment,
                        $invoices_data,
                        $payment->id,
                        $currentDate,
                        $payment->receipt_number,
                        $advance,
                        $payment->organization_id,
                        $payment->student_id
                    );
                }
                return $payment;
            });

            DB::commit();
            return response()->json([
                'payments' => $formattedPayments,
            ]);
        } catch (\Exception $exp) {
            // Log the exception
            Log::error($exp);
            // Rollback the transaction
            DB::rollBack();

            return ['status' => false, 'roleBack' => 'Yes'];
        }
    }

    protected function get_student_pending_invoices($student_id, $advance = false, $type = false, $organization_id = null)
    {

        $invoices = [];
        if (!$organization_id) {
            $organization_id = Auth::user()->organization_id;
        }

        if (isset($student_id)) {

            $invoices = Invoice::whereHas('invoice_detail.student_charge', function ($query) {
                $query->where('charge_status', '!=', 'Paid');
            });
            if ($type) {
                $invoices =
                    $invoices->whereHas('student', function ($query) use ($student_id) {
                        $query->where('student_id', $student_id);
                    });
            } else {
                $invoices = $invoices->where('student_id', $student_id)->where('organization_id', $organization_id);
            }

            if (!$advance) {
                $invoices = $invoices->whereHas('invoice_batch', function ($query) {
                    $query->where('batch_status', 'Posted');
                });
            }

            $invoices = $invoices->where(function ($query) {
                $query->where('invoice_status_code', 'Pending')
                    ->orWhere('invoice_status_code', 'Partially Paid');
            })
                ->with([
                    'invoiceTax',
                    'payment.invoice',
                    'invoice_detail.charge_type.charge_category',
                    'invoice_detail.student_charge' => function ($query) {
                        $query->where('charge_status', '!=', 'Paid');
                    },
                    'invoice_detail.student_charge.invoice_payment',
                ])
                ->orderBy('id', 'asc')
                ->get()
                ->map(function ($invoice) {
                    $totalCharge = $discount = 0;

                    $array = [];

                    foreach ($invoice->invoice_detail as $detail) {
                        if ($detail->student_charge !== null) {
                            // return $detail;
                            if ($detail?->charge_type?->is_discount == 1) {
                                $discount += $detail->charge_amount;
                            } else {
                                $totalCharge += $detail->charge_amount;
                            }
                            if ($detail?->student_charge && count($detail?->student_charge?->invoice_payment) > 0) {
                                $invoice_payment = $detail?->student_charge?->invoice_payment;
                                $paid_amount = 0;
                                foreach ($invoice_payment as $paymentDetail) {
                                    $paid_amount += $paymentDetail?->pivot?->paid_amount;
                                }
                                $totalCharge = $totalCharge - $paid_amount;
                                $array[] = $totalCharge;
                            }
                            $totalCharge = $totalCharge -  $detail?->student_charge->discount_amount;
                        }
                    }
                    // return $array;
                    // return $totalCharge . '=-=-=-' . $discount;

                    $paidTax = 0;
                    $paidTax = Invoice::where('id', $invoice->id)
                        ->with('invoiceTax')
                        ->get()
                        ->pluck('invoiceTax')
                        ->flatten()
                        ->sum('tax_amount');

                    // $invoice->total_charge = $paidTax ? ($totalCharge - $discount) + $paidTax : $totalCharge - $discount;
                    $invoice->total_charge = $paidTax ? ($totalCharge) + $paidTax : $totalCharge;
                    $invoice->discount = $discount;

                    return $invoice;
                })
                ->toArray();
        }

        return $invoices;
    }

    protected function paymentProcess($data, $invoices_data, $payment_id, $date, $receiptNo, $advance = false, $organization_id = null, $std_id = null)
    {
        $returnData = [];
        if (!$organization_id) {
            $organization_id = Auth::user()->organization_id;
        }

        if (isset($data->payment_type_code) && !empty($data->payment_type_code) && $data->payment_type_code == 'Advance') {
            $payAmount = $data->payment_amount - $data->applied_amount;
            $remaining_payment = $data->payment_amount - $data->applied_amount;
        } else {
            $payAmount = isset($data->payment_amount) && !empty($data->payment_amount) ? $data->payment_amount : null;
            $remaining_payment = isset($data->payment_amount) && !empty($data->payment_amount) ? $data->payment_amount : $data['payment_amount'];
        }
        $rest_pay_amount = $applied_amount = $discount =  0;

        // return $invoices_data;
        foreach ($invoices_data as $invoiceKey => $invoice) {

            if ($rest_pay_amount == 0) {
                $rest_pay_amount = $payAmount;
            }

            $invoiceStatus = $invoice['invoice_status_code'];

            if ($invoiceStatus == 'Partially Paid') {
                $partiallyPaid = StudentCharge::with(['invoicePaymentStudentCharge'])
                    ->whereHas('student.enrollmentForSchoolYearForScript.organizationGradeLevel', function ($query) use ($organization_id) {
                        $query->where('organization_id', $organization_id);
                    })
                    ->where(['student_id' => $invoice['student_id'], 'charge_status' => 'Partially Paid'])
                    ->first();

                if ($partiallyPaid) {
                    $charge_amount = $partiallyPaid['charge_amount'] - $partiallyPaid['discount_amount'];

                    $invoice_paid_amount = 0; // Set a default value if 'paid_amount' is not set
                    if (isset($partiallyPaid['invoicePaymentStudentCharge'])) {
                        foreach ($partiallyPaid['invoicePaymentStudentCharge'] as $invoice_payment) {
                            $invoice_paid_amount += $invoice_payment['paid_amount'];
                        }
                    }
                    $pending_charge_amount = $charge_amount - $invoice_paid_amount;

                    if ($pending_charge_amount <= $rest_pay_amount) {
                        $invoice_amount = $pending_charge_amount;
                        $rest_pay_amount -= $invoice_amount;
                    } else {
                        $invoice_amount = $rest_pay_amount;
                        $rest_pay_amount -= $pending_charge_amount;
                    }
                } else {
                    $invoice_amount = min($invoice['total_charge'], $rest_pay_amount);
                    $rest_pay_amount -= $invoice['total_charge'];
                }
            } else {
                $invoice_amount = min($invoice['invoice_subtotal'], $rest_pay_amount);
                $rest_pay_amount -= $invoice['invoice_subtotal'];
            }

            $invoicePayment = InvoicePayment::create([
                'invoice_id' => $invoice['id'],
                'payment_id' => $payment_id,
                'payment_amount' => count($invoices_data) == 1 ? $payAmount : $invoice_amount,
                'applied_date' => $date,
            ]);

            $invoicePaymentas[] = $invoicePayment;

            $filteredInvoiceDetail = array_filter($invoice['invoice_detail'], fn($item) => $item['student_charge'] !== null);
            $invoice['invoice_detail'] = array_values($filteredInvoiceDetail);

            if (isset($invoice['invoice_detail']) && !empty($invoice['invoice_detail'])) {
                $breakCheck = false;
                $datareturn = [];
                foreach ($invoice['invoice_detail'] as $key => $invoice_detail) {

                    if ($invoice_detail['charge_type']['is_discount'] != 1) {

                        if ($invoice_detail['student_charge']['charge_status'] && $invoice_detail['student_charge']['charge_status'] == 'Partially Paid') {
                            $paid_amount = 0;
                            foreach ($invoice_detail['student_charge']['invoice_payment'] as $invoice_payment) {
                                $paid_amount +=  $invoice_payment['pivot']['paid_amount'];
                            }
                            $charge_amount = $invoice_detail['student_charge']['charge_amount'] - ($invoice_detail['student_charge']['discount_amount'] + $paid_amount);
                        } else {
                            $charge_amount = $invoice_detail['charge_amount'] - $invoice_detail['student_charge']['discount_amount'];
                        }

                        if ($payAmount > 0) {
                            $charge_paid_amount = min($payAmount, $charge_amount);
                            $stdChargeUpdateStatus = '';
                            if ($payAmount == $charge_amount) {
                                $stdChargeUpdateStatus = 'Paid';
                            } else {
                                $stdChargeUpdateStatus = ($payAmount < $charge_amount) ? 'Partially Paid' : 'Paid';
                            }
                            $payAmount -= $charge_paid_amount;

                            StudentCharge::where(['id' => $invoice_detail['student_charge_id']])
                                ->update(['charge_status' => $stdChargeUpdateStatus]);

                            $datareturn[] = InvoicePaymentStudentCharge::create([
                                'invoice_payment_id' => $invoicePayment->id,
                                'student_charge_id' => $invoice_detail['student_charge_id'],
                                'paid_amount' => $charge_paid_amount,
                            ]);
                        } else {
                            $breakCheck = true;
                            break;
                        }
                    } else {
                        $StudentChargeDiscount = StudentCharge::where('id', $invoice_detail['student_charge_id'])->first();

                        if ($StudentChargeDiscount) {
                            $StudentChargeDiscount->update(['charge_status' => 'Paid']);
                        }
                    }
                }
            }

            $totalPaidAmount = 0;

            foreach ($datareturn as $payment) {
                $totalPaidAmount += $payment['paid_amount'];
            }

            // Find the InvoicePayment by ID and update the payment_amount
            $invoicePayment = InvoicePayment::find($invoicePayment->id);
            if ($invoicePayment) {
                $invoicePayment->update([
                    'payment_amount' => $totalPaidAmount,
                ]);
            }

            $invoiceUpdateStatus = '';


            // if ($advance) {
            //     $payableTotalCharge = $invoice['total_charge'];

            //     if ($remaining_payment == $payableTotalCharge) {
            //         $invoiceUpdateStatus = 'Paid';
            //     } elseif ($payAmount == 0 && ($remaining_payment < $payableTotalCharge)) {
            //         $invoiceUpdateStatus = 'Partially Paid';
            //     } elseif ($payAmount > 0 && ($remaining_payment > $payableTotalCharge)) {
            //         $invoiceUpdateStatus = 'Paid';
            //     } else {
            //         $invoiceUpdateStatus = 'Paid';
            //     }
            //     $returnData[] = $invoiceUpdateStatus;

            //     // return $invoiceUpdateStatus;
            //     $returnData[] = Invoice::where(['id' => $invoice['id']])
            //         ->update(['invoice_status_code' => $invoiceUpdateStatus]);

            //     $checkInvoicePayment = 0;
            //     if ($remaining_payment <= $payableTotalCharge) {
            //         $checkInvoicePayment = 1;
            //         $applied_amount = isset($data->applied_amount) && !empty($data->applied_amount) && $data->applied_amount > 0
            //             ? $data->applied_amount + $remaining_payment
            //             : $remaining_payment;
            //     } else {
            //         $checkInvoicePayment = 0;
            //         $applied_amount = isset($data->applied_amount) && !empty($data->applied_amount) ? $data->applied_amount + $payableTotalCharge : $data->applied_amount + $payableTotalCharge;
            //     }
            //     $returnData[] = $applied_amount;

            //     $returnData[] = Payment::where(['id' => $data->id])
            //         ->update([
            //             'check_invoice_payment' => $checkInvoicePayment,
            //             'applied_amount' => $applied_amount,
            //         ]);
            // } else {
            //     $payableTotalCharge = $invoice['total_charge'];

            //     if ($remaining_payment == $payableTotalCharge) {
            //         $invoiceUpdateStatus = 'Paid';
            //         $checkInvoicePayment = 1;
            //     } elseif ($payAmount == 0 && ($remaining_payment < $payableTotalCharge)) {
            //         $checkInvoicePayment = 0;
            //         $invoiceUpdateStatus = 'Partially Paid';
            //     } elseif ($payAmount > 0 && ($remaining_payment > $payableTotalCharge)) {
            //         $checkInvoicePayment = 1;
            //         $invoiceUpdateStatus = 'Paid';
            //     } else {
            //         $invoiceUpdateStatus = 'Paid';
            //         $checkInvoicePayment = 1;
            //     }


            //     if ($remaining_payment <= $payableTotalCharge) {
            //         $applied_amount = isset($data->applied_amount) && !empty($data->applied_amount) && $data->applied_amount > 0
            //             ? $data->applied_amount + $remaining_payment
            //             : $remaining_payment;
            //     } else {
            //         $applied_amount = $data->applied_amount + $payableTotalCharge;
            //     }

            //     $returnData[] = Invoice::where(['id' => $invoice['id']])
            //         ->update(['invoice_status_code' => $invoiceUpdateStatus]);

            //     $returnData[] = Payment::where(['id' => $payment_id])
            //         ->update([
            //             'applied_amount' => $applied_amount,
            //             'check_invoice_payment' => $checkInvoicePayment,
            //         ]);

            //     $remaining_payment = $payAmount;
            // }


            $payableTotalCharge = $invoice['total_charge'];

            if ($remaining_payment == $payableTotalCharge) {
                $invoiceUpdateStatus = 'Paid';
                $checkInvoicePayment = 1;
            } elseif ($payAmount == 0 && $remaining_payment < $payableTotalCharge) {
                $invoiceUpdateStatus = 'Partially Paid';
                $checkInvoicePayment =  1;
            } elseif ($payAmount > 0 && $remaining_payment > $payableTotalCharge) {
                $checkInvoicePayment =  0;
                $invoiceUpdateStatus = 'Paid';
            } else {
                $invoiceUpdateStatus = 'Paid';
                $checkInvoicePayment =  1;
            }

            Invoice::where('id', $invoice['id'])->update([
                'invoice_status_code' => $invoiceUpdateStatus
            ]);

            if ($remaining_payment <= $payableTotalCharge) {
                $applied_amount = isset($data->applied_amount) && !empty($data->applied_amount) && $data->applied_amount > 0
                    ? $data->applied_amount + $remaining_payment
                    : $applied_amount + $remaining_payment;
            } else {
                $applied_amount = isset($data->applied_amount) && !empty($data->applied_amount) && $data->applied_amount > 0
                    ? $data->applied_amount + $payableTotalCharge
                    : $applied_amount + $payableTotalCharge;
            }

            Payment::where('id', $payment_id)
                ->update([
                    'applied_amount' => $applied_amount,
                    'check_invoice_payment' => $checkInvoicePayment,
                ]);

            $remaining_payment = $payAmount;


            if ($payAmount == 0) {
                break;
            }
        }

        return $returnData;
    }

    public function updateAppliedPayment(Request $request)
    {
        DB::statement("
            UPDATE payments p
            JOIN (
                SELECT
                    payment_id,
                    COALESCE(SUM(payment_amount), 0) AS total_applied_amount
                FROM invoice_payment
                GROUP BY payment_id
            ) ip ON p.id = ip.payment_id
            SET
                p.applied_amount = ip.total_applied_amount,
                p.check_invoice_payment = 0
            WHERE
                p.id NOT IN (
                    5333, 5334, 5341, 6633, 6669, 6696, 6698, 6711, 6727,
                    6731, 6739, 6741, 6746, 6752, 6755, 6770, 6773, 6775, 6777
                )
                AND p.payment_amount <> ip.total_applied_amount
        ");

        return response()->json(['status' => 'success', 'message' => 'Payments updated successfully.']);
    }

    public function updatePaymentInvoiceData(Request $request)
    {
        DB::statement("
            UPDATE payments
            SET applied_amount = 0, check_invoice_payment = 0
            WHERE id NOT IN (
                SELECT payment_id FROM invoice_payment
            )
            AND id NOT IN (
                5333, 5334, 5341, 6633, 6669, 6696, 6698, 6711, 6727,
                6731, 6739, 6741, 6746, 6752, 6755, 6770, 6773, 6775, 6777
            )
        ");

        return response()->json(['status' => 'success', 'message' => 'Payments updated successfully.']);
    }
}
