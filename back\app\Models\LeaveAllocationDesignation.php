<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeaveAllocationDesignation extends Model
{
    use HasFactory;
    protected $table = 'leave_allocations_designation';

    protected $fillable = [
        'designation_id',
        'leave_allocation_id',
        'employment_length',
    ];

    // Relationships (optional)
    public function designation()
    {
        return $this->belongsTo(Designation::class);
    }

    public function leaveAllocation()
    {
        return $this->belongsTo(LeaveAllocation::class);
    }
}
