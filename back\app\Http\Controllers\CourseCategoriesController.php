<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\CourseCategories;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\StoreCourseCategoriesRequest;
use App\Http\Requests\UpdateCourseCategoriesRequest;
use App\Repositories\Interfaces\CourseCategoriesRepositoryInterface;
use Illuminate\Support\Facades\Log;

class CourseCategoriesController extends Controller
{
    use ApiErrorResponse;

    private $CourseCategoriesRepositoryInterface;

    
            public function __construct(CourseCategoriesRepositoryInterface $CourseCategoriesRepositoryInterface)
            {
                $this->CourseCategoriesRepositoryInterface = $CourseCategoriesRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->CourseCategoriesRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreCourseCategoriesRequest $request)
            {
                try {
                    DB::beginTransaction();
                    $state = $this->CourseCategoriesRepositoryInterface->store($request);
                    DB::commit();
                    return $this->successResponse(false, config('constants.DATA_INSERTED'));
                } catch (\Exception $e) {
                    Log::error($e);
                    DB::rollBack();
                    return $this->generalError(config('constants.GENERAL_ERROR'));
                }                                
            }

            /**
             * Display the specified resource.
             */
            public function show($CourseCategories)
            {
                $state = $this->CourseCategoriesRepositoryInterface->show($CourseCategories);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateCourseCategoriesRequest $request, CourseCategories $CourseCategories)
            {
                try {
                    DB::beginTransaction();
                    $state = $this->CourseCategoriesRepositoryInterface->update($request, $CourseCategories);
                    DB::commit();
                    return $this->successResponse(false, config('constants.DATA_UPDATED'));
                } catch (\Exception $e) {
                    Log::error($e);
                    DB::rollBack();
                    return $this->generalError(config('constants.GENERAL_ERROR'));
                }  
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($CourseCategories)
            {
                $state = $this->CourseCategoriesRepositoryInterface->destroy($CourseCategories);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));
                }
            }
            
}