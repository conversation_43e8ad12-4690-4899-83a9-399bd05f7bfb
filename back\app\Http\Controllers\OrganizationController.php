<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreOrganizationRequest;
use App\Models\Organization;
use App\Repositories\Interfaces\OrganizationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class OrganizationController extends Controller
{
    use ApiErrorResponse;

    private $OrganizationRepositoryInterface;

    public function __construct(OrganizationRepositoryInterface $OrganizationRepositoryInterface)
    {
        $this->OrganizationRepositoryInterface = $OrganizationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->OrganizationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreOrganizationRequest $request)
    {
        $state = $this->OrganizationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(Organization $Organization)
    {
        $state = $this->OrganizationRepositoryInterface->show($Organization->id);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Organization $Organization)
    {
        $state = $this->OrganizationRepositoryInterface->update($request, $Organization);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Organization)
    {
        $state = $this->OrganizationRepositoryInterface->destroy($Organization);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function getLoginUserOrganizations(Request $request)
    {
        $result['data'] = $this->OrganizationRepositoryInterface->getLoginUserOrganizations($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getOrganizationRole(Request $request)
    {
        $result = $this->OrganizationRepositoryInterface->getOrganizationRole($request->all());

        return response()->json($result, Response::HTTP_OK);
    }
}
