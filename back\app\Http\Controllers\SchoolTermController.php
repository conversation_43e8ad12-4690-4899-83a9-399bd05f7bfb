<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSchoolTermRequest;
use App\Http\Requests\UpdateSchoolTermRequest;
use App\Models\SchoolTerm;
use App\Repositories\Interfaces\SchoolTermRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SchoolTermController extends Controller
{
    use ApiErrorResponse;

    private $SchoolTermRepositoryInterface;

    public function __construct(SchoolTermRepositoryInterface $SchoolTermRepositoryInterface)
    {
        $this->SchoolTermRepositoryInterface = $SchoolTermRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SchoolTermRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSchoolTermRequest $request)
    {
        $state = $this->SchoolTermRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($SchoolTerm)
    {
        $state = $this->SchoolTermRepositoryInterface->show($SchoolTerm);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSchoolTermRequest $request, SchoolTerm $SchoolTerm)
    {
        $state = $this->SchoolTermRepositoryInterface->update($request, $SchoolTerm);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($SchoolTerm)
    {
        $state = $this->SchoolTermRepositoryInterface->destroy($SchoolTerm);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
