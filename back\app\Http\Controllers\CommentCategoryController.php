<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CommentCategory;
use App\Http\Requests\StoreCommentCategoryRequest;
use App\Http\Requests\UpdateCommentCategoryRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\CommentCategoryRepositoryInterface;
use App\Traits\ApiErrorResponse;

class CommentCategoryController extends Controller
{
    use ApiErrorResponse;

    private $CommentCategoryRepositoryInterface;

    
            public function __construct(CommentCategoryRepositoryInterface $CommentCategoryRepositoryInterface)
            {
                $this->CommentCategoryRepositoryInterface = $CommentCategoryRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->CommentCategoryRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreCommentCategoryRequest $request)
            {
                $state = $this->CommentCategoryRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($CommentCategory)
            {
                $state = $this->CommentCategoryRepositoryInterface->show($CommentCategory);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateCommentCategoryRequest $request, CommentCategory $CommentCategory)
            {
                $state = $this->CommentCategoryRepositoryInterface->update($request, $CommentCategory);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($CommentCategory)
            {
                $state = $this->CommentCategoryRepositoryInterface->destroy($CommentCategory);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}