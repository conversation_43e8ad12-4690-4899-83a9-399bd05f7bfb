<?php

namespace App\Http\Requests;;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLeaveAllocationRequest extends FormRequest
{

             /**
             * Determine if the user is authorized to make this request.
             */
            public function authorize(): bool
            {
                return true;
            }

            /**
             * Get the validation rules that apply to the request.
             *
             * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
             */
            public function rules(): array
            {
                $Id = $this->get('id');
                return [
                    'allocation_type' => ['sometimes', 'string'],
                    'designation_id' => ['sometimes', 'exists:designations,id'],
                    'leave_type_id' => ['sometimes', 'exists:leave_types,id'],
                    'no_of_days' => ['sometimes', 'integer', 'min:1'],
                    'max_days_carry_forward' => ['sometimes', 'integer', 'min:0'],
                    'carry_forward' => ['sometimes', 'boolean'],
                    'document_required' => ['sometimes', 'boolean'],
                    'max_leaves_together' => ['sometimes', 'integer', 'min:0'],
                    'start_date' => ['sometimes', 'date'],
                    'end_date' => ['sometimes', 'date', 'after_or_equal:start_date'],
                    'id' => ['sometimes', 'integer', Rule::exists('leave_allocations', 'id')],
                ];
            }

            /**
             * Get the error messages for the defined validation rules.
             *
             * @return array<string, string>
             */
            public function messages(): array
            {
                return [
                    'name.required' => 'The leave allocation name is required.',
                    'name.max' => 'The leave allocation name must not exceed 255 characters.',
                    'name.unique' => 'The leave allocation name is already taken. Please choose a different name.',
                ];
            }
        
}