<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateManageGuardianRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');
        $userId = $this->get('user_id');

        return [
            'first_name' => ['required', 'max:255'],
            'last_name' => ['required', 'max:255'],
            'identification_type_id' => ['required'],
            'communication_preference' => ['required'],
            // 'other_info' => ['required'],
            'address_type_id' => 'required',
            'province_id' => 'required',
            'city' => 'required',
            'postal_code' => 'required',
            'address_1' => 'required',
            'email' => [
                'nullable', // This allows the email to be empty
                'email',
                Rule::unique('users', 'email')->ignore($userId, 'id'), // Ensures unique email except for the current user
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'The first name is required.',
            'last_name.required' => 'The last name is required.',
            'identification_type_id.required' => 'The identification type is required.',
            'communication_preference.required' => 'The communication preference is required.',
            // 'other_info.required' => 'The other info is required.',
            'address_type_id.required' => 'Address type is required.',
            'province_id.required' => 'Province is required.',
            'city.required' => 'City is required.',
            'postal_code.required' => 'Postal code is required.',
            'address_1.required' => 'Address is required.',
        ];
    }
}
