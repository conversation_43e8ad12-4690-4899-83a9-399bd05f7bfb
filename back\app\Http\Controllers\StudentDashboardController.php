<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentDashboardRequest;
use App\Http\Requests\UpdateStudentDashboardRequest;
use App\Models\StudentDashboard;
use App\Models\Student;
use App\Repositories\Interfaces\StudentDashboardRepositoryInterface;
use App\Repositories\Interfaces\StudentGradingRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class StudentDashboardController extends Controller
{
    use ApiResponse;

    private $StudentDashboardRepositoryInterface;
    private $StudentGradingRepositoryInterface;

    public function __construct(StudentDashboardRepositoryInterface $StudentDashboardRepositoryInterface, StudentGradingRepositoryInterface $StudentGradingRepositoryInterface)
    {
        $this->StudentDashboardRepositoryInterface = $StudentDashboardRepositoryInterface;
        $this->StudentGradingRepositoryInterface = $StudentGradingRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $studentData = $this->StudentDashboardRepositoryInterface->index();
            $data['students'] = $studentData['studentData'];
            $data['studentWisePaymentHistory'] = $studentData['studentWisePaymentHistory'];
            $data['studentPendingInvoicesArray'] = $studentData['studentPendingInvoicesArray'];
            $data['studentLeaveDataArray'] = $studentData['studentLeaveDataArray'];
            $data['studentPaymentHistory'] = $studentData['studentPaymentHistory'];
            $data['studentAssignmentQuizArray'] = $studentData['studentAssignmentQuizArray'];
            $data['account_balance'] = $this->StudentDashboardRepositoryInterface->accountBalance($data['students']);
            $data['guardian'] = $this->StudentDashboardRepositoryInterface->getGuardian();

            return response()->json($data, Response::HTTP_OK);
        } catch (\Exception $exp) {
            Log::error($exp);
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentDashboardRequest $request)
    {
        $state = $this->StudentDashboardRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($StudentDashboard)
    {
        $state = $this->StudentDashboardRepositoryInterface->show($StudentDashboard);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentDashboardRequest $request, StudentDashboard $StudentDashboard)
    {
        $state = $this->StudentDashboardRepositoryInterface->update($request, $StudentDashboard);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentDashboard)
    {
        $state = $this->StudentDashboardRepositoryInterface->destroy($StudentDashboard);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function getStudentDashboardInformation()
    {
        $state = $this->StudentDashboardRepositoryInterface->getStudentDashboardInformation();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentAccountInformation()
    {
        $state = $this->StudentDashboardRepositoryInterface->getStudentAccountInformation();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentGradesInformation()
    {
        $state = $this->StudentDashboardRepositoryInterface->getStudentGradesInformation();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentCourses()
    {
        $state = $this->StudentDashboardRepositoryInterface->getStudentCourses();

        return response()->json($state, Response::HTTP_OK);
    }

    public function getStudentAttendanceOverall()
    {
        $state = $this->StudentDashboardRepositoryInterface->getStudentAttendanceOverall();

        return response()->json($state, Response::HTTP_OK);
    }
    public function getSingleStudentGraphData(Request $request)
    {
        $data = $request->all();
        $studentID = $data['id'];
        $schoolYear = activeSchoolYear()->id ?? null;
        $Student = Student::with([
            'classes' => function ($query) use ($schoolYear) {
                $query->where('school_year_id', $schoolYear);
            },
            'classes.class_student' => function ($query) use ($schoolYear, $studentID) {
                $query->where('student_id', $studentID);
            },
            'classes.class_student.manage_class.class_courses.course'
        ])->where('id', $studentID)->first();

        $data['class_student_id_view'] = $Student?->classes[0]?->class_student[0]?->id ?? 0;
        $data['school_year_id'] = $schoolYear;
        $data['class_id'] = $Student?->classes[0]?->id ?? 0;
        $data['grade_level_id'] = $Student?->classes[0]?->organization_grade_level_id ?? 0;
        $data['term_ids'] = $data['schoolTerm'] ?? null;

        // term_id: 4
        // class_course_id: 64
        $newRequest = new Request($data);
        $state = $this->StudentGradingRepositoryInterface->getSingleStudentGraphData($newRequest);
        return response()->json($state, Response::HTTP_OK);
    }
}
