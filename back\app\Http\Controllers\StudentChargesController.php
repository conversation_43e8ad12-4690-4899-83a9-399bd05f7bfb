<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentChargesRequest;
use App\Http\Requests\UpdateStudentChargesRequest;
use Illuminate\Http\Request;
use App\Models\StudentCharge;
use App\Repositories\Interfaces\StudentChargesRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class StudentChargesController extends Controller
{
    use ApiErrorResponse;

    private $StudentChargesRepositoryInterface;

    public function __construct(StudentChargesRepositoryInterface $StudentChargesRepositoryInterface)
    {
        $this->StudentChargesRepositoryInterface = $StudentChargesRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->StudentChargesRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentChargesRequest $request)
    {
        $state = $this->StudentChargesRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    public function bulkDeleteFilterData(Request $requestData)
    {
        $state = $this->StudentChargesRepositoryInterface->bulkDeleteFilterData($requestData);
        return response()->json($state, Response::HTTP_OK);
    }
    /**
     * Display the specified resource.
     */
    public function show($StudentCharges)
    {
        $state = $this->StudentChargesRepositoryInterface->show($StudentCharges);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentChargesRequest $request, StudentCharge $StudentCharges)
    {
        $state = $this->StudentChargesRepositoryInterface->update($request, $StudentCharges);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($StudentCharges)
    {
        $state = $this->StudentChargesRepositoryInterface->destroy($StudentCharges);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function deleteStudentCharges(Request $StudentCharges)
    {
        $state = $this->StudentChargesRepositoryInterface->deleteStudentCharges($StudentCharges->all());
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function studentChargesOverview(Request $requestData)
    {
        $state = $this->StudentChargesRepositoryInterface->studentChargesOverview($requestData);
        return response()->json($state, Response::HTTP_OK);
    }

    public function studentChargesBreakdown(Request $requestData)
    {
        $state = $this->StudentChargesRepositoryInterface->studentChargesBreakdown($requestData);
        return response()->json($state, Response::HTTP_OK);
    }

    public function singleStudentCharges(Request $requestData)
    {
        $state = $this->StudentChargesRepositoryInterface->singleStudentCharges($requestData);
        return $state['status'] ? $this->successResponse(false, $state['message']) : '';
    }
}
