<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\IncidentType;
use App\Http\Requests\StoreIncidentTypeRequest;
use App\Http\Requests\UpdateIncidentTypeRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\IncidentTypeRepositoryInterface;
use App\Traits\ApiErrorResponse;

class IncidentTypeController extends Controller
{
    use ApiErrorResponse;

    private $IncidentTypeRepositoryInterface;

    
            public function __construct(IncidentTypeRepositoryInterface $IncidentTypeRepositoryInterface)
            {
                $this->IncidentTypeRepositoryInterface = $IncidentTypeRepositoryInterface;
            }
        

    
            /**
             * Display a listing of the resource.
             */
            public function index()
            {
                $state = $this->IncidentTypeRepositoryInterface->index();
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Store a newly created resource in storage.
             */
            public function store(StoreIncidentTypeRequest $request)
            {
                $state = $this->IncidentTypeRepositoryInterface->store($request);
                return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
            }

            /**
             * Display the specified resource.
             */
            public function show($IncidentType)
            {
                $state = $this->IncidentTypeRepositoryInterface->show($IncidentType);
                return response()->json($state, Response::HTTP_OK);
            }

            /**
             * Update the specified resource in storage.
             */
            public function update(UpdateIncidentTypeRequest $request, IncidentType $IncidentType)
            {
                $state = $this->IncidentTypeRepositoryInterface->update($request, $IncidentType);
                return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
            }

            /**
             * Remove the specified resource from storage.
             */
            public function destroy($IncidentType)
            {
                $state = $this->IncidentTypeRepositoryInterface->destroy($IncidentType);
                if ($state != 1 && $state == 23000) {
                    return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
                } else {
                    return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                        $this->generalError(config('constants.GENERAL_ERROR'));;
                }
            }
            
}