<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Guardian;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\{DB};
use Illuminate\Support\Facades\Validator;
use App\Traits\{ApiResponse, PreLoadTrait};
use App\Http\Requests\StudentPersonalInfoReuest;
use App\Repositories\Interfaces\StudentRepositoryInterface;

class StudentController extends Controller
{
    use ApiResponse, PreLoadTrait;

    private $StudentRepositoryInterface;

    public function __construct(StudentRepositoryInterface $StudentRepositoryInterface)
    {
        $this->StudentRepositoryInterface = $StudentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $result = $this->StudentRepositoryInterface->index();

        return response()->json($result, Response::HTTP_OK);
    }

    // get All Students
    public function getAllStudentsDropdown(Request $request)
    {
        $result = $this->StudentRepositoryInterface->getAllStudentsDropdown($request->all());

        return response()->json($result, Response::HTTP_OK);
    }
    /** student preLoaded Data  */
    public function getPreLoadedStudentData()
    {
        $result['data'] =  $this->StudentRepositoryInterface->getPreLoadedStudentData();

        return response()->json($result, Response::HTTP_OK);
    }

    public function getStudentPersonalInfo(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentPersonalInfo($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /** save student personal Information */
    public function addStudentPersonalInfo(StudentPersonalInfoReuest $request)
    {
        try {
            $create_user_email = DB::table('system_settings')
                ->where('name', 'create_user_email')
                ->first()?->value == 1 ? true : false;

            if ($create_user_email && $studentEmail = $request->get('student_email')) {
                $request->merge(['email' => $studentEmail]);

                $userId = null;
                if ($studentId = $request->get('id')) {
                    $student = Student::find($studentId);
                    $userId = $student?->user?->id;
                }

                $request->merge(['user_id' => $userId]);
                $this->userValidation($request);
            }
            DB::beginTransaction();
                $result = $this->StudentRepositoryInterface->addStudentPersonalInfo($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            $errorMsg = $exp->getMessage();
            return $this->generalError($errorMsg);
        }

        // $result = $this->StudentRepositoryInterface->addStudentPersonalInfo($request);
        // return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /** remove student picture */
    public function deleteStudentPicture(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentRepositoryInterface->deleteStudentPicture($request);
            DB::commit();

            return $this->successResponse($result, 'Picture removed.');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getStudentMedicalInfo(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentMedicalInfo($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /** medical */
    public function addStudentMedicalInfo(Request $request)
    {
        $result = $this->StudentRepositoryInterface->addStudentMedicalInfo($request);

        return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    /**student guardian */
    public function getStudentGuardians(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentGuardians($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function addStudentGuardian(Request $request)
    {
        try {
            if ($guardianData = Guardian::find($request->get('guardian_id'))) {
                $request->merge(['user_id' => $guardianData->user?->id]);
            }
            $this->userValidation($request);


            DB::beginTransaction();
            $result = $this->StudentRepositoryInterface->addStudentGuardian($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            $errorMsg = $exp->getMessage();
            return $this->generalError($errorMsg);
        }
    }

    public function addStudentCustodialInformation(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentRepositoryInterface->addStudentCustodialInformation($request->all());
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_INSERTED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            $errorMsg = $exp->getMessage();
            return $this->generalError($errorMsg);
        }
    }

    protected function userValidation($requestData)
    {
        $userId = $requestData->get('user_id') ?? '';
        $validator = Validator::make($requestData->all(), [
            'email' => "nullable|email|unique:users,email,{$userId}",
        ]);

        if ($validator->fails()) {
            throw new \Exception($validator->errors()->first());
        }
        // $userId = $requestData->get('id');
        // $validator = Validator::make($requestData->all(), [
        //     'email'      => 'nullable|email|unique:users,email,' . $userId ?? '',
        // ]);
        // if ($validator->fails()) {
        //     throw new \Exception($validator->errors()->first());
        // }

    }

    public function makePrimaryContact(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentRepositoryInterface->makePrimaryContact($request);
            DB::commit();
            return $this->successResponse($result, config('constants.DATA_UPDATED'));
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }
    public function deleteStudentGuardian(Request $request)
    {
        $result = $this->StudentRepositoryInterface->deleteStudentGuardian($request);

        return $this->successResponse($result, config('constants.DATA_DELETED'));
    }

    /**student siblings */
    public function getStudentSiblings(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentSiblings($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getGuardianChildrens(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getGuardianChildrens($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function addStudentSibiling(Request $request)
    {
        $result = $this->StudentRepositoryInterface->addStudentSibiling($request);

        return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function deleteStudentSibling(Request $request)
    {
        $result = $this->StudentRepositoryInterface->deleteStudentSibling($request);

        return $this->successResponse($result, config('constants.DATA_DELETED'));
    }

    public function getStudentEnrollments(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentEnrollments($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function addStudentEnrollment(Request $request)
    {
        $result = $this->StudentRepositoryInterface->addStudentEnrollment($request);

        return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function deleteStudentEnrollment(Request $request)
    {
        $result = $this->StudentRepositoryInterface->deleteStudentEnrollment($request);

        return $this->successResponse($result, config('constants.DATA_DELETED'));
    }

    /**get all document types */
    public function getAllDocumentTypes(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getAllDocumentTypes($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function getStudentDocuments(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentDocuments($request);

        return response()->json($result, Response::HTTP_OK);
    }

    public function addStudentDocument(Request $request)
    {
        $result = $this->StudentRepositoryInterface->addStudentDocument($request);

        return $result ? $this->successResponse($result, config('constants.DATA_INSERTED')) : $this->generalError(config('constants.GENERAL_ERROR'));
    }

    public function deleteStudentDocument(Request $request)
    {
        $result = $this->StudentRepositoryInterface->deleteStudentDocument($request);

        return $this->successResponse($result, config('constants.DATA_DELETED'));
    }

    public function getStudentActiveEnrollment(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->getStudentActiveEnrollment($request);

        return response()->json($result, Response::HTTP_OK);
    }

    /** student search */
    public function searchStudents(Request $request)
    {
        $result['data'] = $this->StudentRepositoryInterface->searchStudents($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function preLoadPromoteStudent(Request $request)
    {
        $result['data'] = $this->getPreLoadTrait($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function preLoadGrades(Request $request)
    {
        $result['data'] = $this->organizationGradeLevels($request);
        return response()->json($result, Response::HTTP_OK);
    }
    public function fetchGradeStudents(Request $request)
    {
        $result['data'] = $this->fetchGradeStudentsTrait($request);
        return response()->json($result, Response::HTTP_OK);
    }

    public function savePromotedStudents(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentRepositoryInterface->savePromotedStudents($request);
            DB::commit();
            return $this->successResponse($result, 'Students promoted successfully');
        } catch (\Exception $exp) {
            DB::rollBack();
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function exportStudentsEnrollment(Request $request)
    {
        $result = $this->StudentRepositoryInterface->exportStudentsEnrollment($request);
        return response()->json([
            'file_url' => $result
        ]);
    }
}
