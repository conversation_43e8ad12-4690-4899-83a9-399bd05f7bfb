<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UpdateSchoolYearRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'name' => [
                'required',
                'max:255',
                $Id ? Rule::unique('school_years')->ignore($Id) : 'unique:school_years',
            ],
            'year_start_end' => [
                'required',
                function ($attribute, $value, $fail) use ($Id) {
                    // Parse the date range from the input value
                    [$start, $end] = $value;

                    $query = DB::table('school_years');
                    if ($Id) {
                        // Exclude the current record when updating
                        $query->where('id', '<>', $Id);
                    }
                    $query->whereRaw('(? BETWEEN start_date AND end_date', [$start])
                        ->orWhereRaw('? BETWEEN start_date AND end_date)', [$end]);

                    $conflict = $query->exists();

                    if ($conflict) {
                        $fail('The selected year range must be unique. Please choose a different range.');
                    }
                },
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The school year name is required.',
            'name.max' => 'The school year name must not exceed 255 characters.',
            'name.unique' => 'The school year name is already taken. Please choose a different name.',
            'year_start_end.required' => 'The school year start and end dates are required.',
            'year_start_end.unique' => 'The selected year range must be unique. Please choose a different range.',
        ];
    }
}
