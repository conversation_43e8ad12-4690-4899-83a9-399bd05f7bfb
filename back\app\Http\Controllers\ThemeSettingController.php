<?php

namespace App\Http\Controllers;

use App\Http\Requests\SaveSmtpRequest;
use App\Http\Requests\StoreThemeSettingRequest;
use App\Http\Requests\UpdateThemeSettingRequest;
use App\Models\ThemeSetting;
use App\Repositories\Interfaces\ThemeSettingRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class ThemeSettingController extends Controller
{
    use ApiErrorResponse;

    private $ThemeSettingRepositoryInterface;

    public function __construct(ThemeSettingRepositoryInterface $ThemeSettingRepositoryInterface)
    {
        $this->ThemeSettingRepositoryInterface = $ThemeSettingRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ThemeSettingRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreThemeSettingRequest $request)
    {
        Log::info($request->all());
        Log::info(auth()->user()->id);
        ThemeSetting::updateOrCreate(
            ['user_id' => auth()->user()->id],
            $request->all()
        );

        return true;
        // $state = $this->ThemeSettingRepositoryInterface->store($request->all());
        // return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ThemeSetting)
    {
        $state = $this->ThemeSettingRepositoryInterface->show($ThemeSetting);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateThemeSettingRequest $request, ThemeSetting $ThemeSetting)
    {
        $state = $this->ThemeSettingRepositoryInterface->update($request, $ThemeSetting);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ThemeSetting)
    {
        $state = $this->ThemeSettingRepositoryInterface->destroy($ThemeSetting);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }

    public function uploadLogo(Request $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->uploadLogo($request);
        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_UPDATED'))
        :
        $this->generalError($state['message']);
    }

    public function getAllLogos()
    {
        $state = $this->ThemeSettingRepositoryInterface->getAllLogos();
        return response()->json($state, Response::HTTP_OK);
    }

    public function deleteLogos($id)
    {
        $state = $this->ThemeSettingRepositoryInterface->deleteLogos($id);

        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_DELETED'))
        :
        $this->generalError($state['message']);

    }

    public function getCommunicationModules()
    {
        $state = $this->ThemeSettingRepositoryInterface->getCommunicationModules();
        return response()->json($state, Response::HTTP_OK);
    }

    public function updateCommunicationModules(Request $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->updateCommunicationModules($request);
        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_UPDATED'))
        :
        $this->generalError($state['message']);
    }

    public function getSmtp(Request $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->getSmtp($request);
        return response()->json($state, Response::HTTP_OK);
    }

    public function saveSmtp(SaveSmtpRequest $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->saveSmtp($request);
        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_UPDATED'))
        :
        $this->generalError($state['message']);
    }

    public function getTimezone()
    {
        $state = $this->ThemeSettingRepositoryInterface->getTimezone();
        return response()->json($state, Response::HTTP_OK);
    }

    public function saveTimezone(Request $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->saveTimezone($request);
        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_UPDATED'))
        :
        $this->generalError($state['message']);
    }

    public function getSystemSettings()
    {
        $state = $this->ThemeSettingRepositoryInterface->getSystemSettings();
        return response()->json($state, Response::HTTP_OK);
    }

    public function saveSystemSettings(Request $request)
    {
        $state = $this->ThemeSettingRepositoryInterface->saveSystemSettings($request);
        return $state['status'] ?
        $this->successResponse(false, config('constants.DATA_UPDATED'))
        :
        $this->generalError($state['message']);
    }
}
