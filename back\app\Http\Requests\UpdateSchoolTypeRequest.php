<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSchoolTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $Id = $this->get('id');

        return [
            'school_type' => [
                'required',
                'max:255',
                $Id ? Rule::unique('school_types')->ignore($Id) : 'unique:school_types',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'school_type.required' => 'The school type name is required.',
            'school_type.max' => 'The school type name must not exceed 255 characters.',
            'school_type.unique' => 'The school type name is already taken. Please choose a different name.',
        ];
    }
}
