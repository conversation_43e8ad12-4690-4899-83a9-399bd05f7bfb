<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTeacherRoleRequest;
use App\Http\Requests\UpdateTeacherRoleRequest;
use App\Models\TeacherRole;
use App\Repositories\Interfaces\TeacherRoleRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class TeacherRoleController extends Controller
{
    use ApiErrorResponse;

    private $TeacherRoleRepositoryInterface;

    public function __construct(TeacherRoleRepositoryInterface $TeacherRoleRepositoryInterface)
    {
        $this->TeacherRoleRepositoryInterface = $TeacherRoleRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->TeacherRoleRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTeacherRoleRequest $request)
    {
        $state = $this->TeacherRoleRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show(TeacherRole $TeacherRole)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTeacherRoleRequest $request, TeacherRole $TeacherRole)
    {
        $state = $this->TeacherRoleRepositoryInterface->update($request, $TeacherRole);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($TeacherRole)
    {
        $state = $this->TeacherRoleRepositoryInterface->destroy($TeacherRole);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
