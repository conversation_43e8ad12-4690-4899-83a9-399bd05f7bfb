<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCommunicationRequest;
use App\Http\Requests\UpdateCommunicationRequest;
use App\Models\Communication;
use App\Repositories\Interfaces\CommunicationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CommunicationController extends Controller
{
    use ApiErrorResponse;

    private $CommunicationRepositoryInterface;

    public function __construct(CommunicationRepositoryInterface $CommunicationRepositoryInterface)
    {
        $this->CommunicationRepositoryInterface = $CommunicationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->CommunicationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCommunicationRequest $request)
    {
        $state = $this->CommunicationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Communication)
    {
        $state = $this->CommunicationRepositoryInterface->show($Communication);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCommunicationRequest $request, Communication $Communication)
    {
        $state = $this->CommunicationRepositoryInterface->update($request, $Communication);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Communication)
    {
        $state = $this->CommunicationRepositoryInterface->destroy($Communication);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
            $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function sent_communication_mail(Request $request)
    {
        $state = $this->CommunicationRepositoryInterface->sent_mail($request);
        if ($state === -1) {
            return $this->generalError(config('constants.MAIL_BLOCKED'));
        }
        return $state ? $this->successResponse(false, config('constants.EMAIL_SENT')) : '';
    }

    public function get_communication_mail(Request $request)
    {

        $result = $this->CommunicationRepositoryInterface->get_communication_mail();

        return response()->json($result, Response::HTTP_OK);
    }

    public function delete_communication_mail($Communication)
    {

        $state = $this->CommunicationRepositoryInterface->delete_communication_mail($Communication);

        return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
    }
    public function get_announcements(Request $request)
    {
        $result = $this->CommunicationRepositoryInterface->get_announcements($request);
        return response()->json($result, Response::HTTP_OK);
    }

    /**
     * Display a listing of the resource.
     */
    public function get_communication_conversation()
    {
        $state = $this->CommunicationRepositoryInterface->get_communication_conversation();

        return response()->json($state, Response::HTTP_OK);
    }

    public function get_users_of_classes(Request $request)
    {
        $state = $this->CommunicationRepositoryInterface->get_users_of_classes($request);
        if($state && isset($state['error'])){
            return $this->generalError($state['error']);
        }
        return response()->json($state, Response::HTTP_OK);
    }
}
