<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreStudentAssignmentRequest;
use App\Http\Requests\UpdateStudentAssignmentRequest;
use App\Models\StudentAssignment;
use App\Repositories\Interfaces\StudentAssignmentRepositoryInterface;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class StudentAssignmentController extends Controller
{
    use ApiResponse;

    private $StudentAssignmentRepositoryInterface;

    public function __construct(StudentAssignmentRepositoryInterface $StudentAssignmentRepositoryInterface)
    {
        $this->StudentAssignmentRepositoryInterface = $StudentAssignmentRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {        
        $data = $this->StudentAssignmentRepositoryInterface->guardianStudentAssignments($request);
        return response()->json($data, Response::HTTP_OK);
    }

    /** single student assignment
     * @var
     * on the basis of
     */
    public function viewSingleStudentAssignment(Request $request)
    {
        try {
            $result['data'] = $this->StudentAssignmentRepositoryInterface->viewSingleStudentAssignment($request);
            return response()->json($result, Response::HTTP_OK);
        } catch (\Exception $exp) {
            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

    public function getSingleStudentAssignments(Request $request)
    {
        // try {
            $result = $this->StudentAssignmentRepositoryInterface->getSingleStudentAssignments($request);
            return response()->json($result, Response::HTTP_OK);
        // } catch (\Exception $exp) {
        //     return $this->generalError(config('constants.GENERAL_ERROR'));
        // }
    }

    public function submitAssignment(Request $request)
    {
        try {
            DB::beginTransaction();
            $result = $this->StudentAssignmentRepositoryInterface->submitAssignment($request);
            DB::commit();

            return $this->successResponse($result, 'Assignment submited successfully.');
        } catch (\Exception $exp) {
            DB::rollBack();

            return $this->generalError(config('constants.GENERAL_ERROR'));
        }
    }

}
