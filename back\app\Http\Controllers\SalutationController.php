<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSalutationRequest;
use App\Http\Requests\UpdateSalutationRequest;
use App\Models\Salutation;
use App\Repositories\Interfaces\SalutationRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Response;

class SalutationController extends Controller
{
    use ApiErrorResponse;

    private $SalutationRepositoryInterface;

    public function __construct(SalutationRepositoryInterface $SalutationRepositoryInterface)
    {
        $this->SalutationRepositoryInterface = $SalutationRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->SalutationRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSalutationRequest $request)
    {
        $state = $this->SalutationRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($Salutation)
    {
        $state = $this->SalutationRepositoryInterface->show($Salutation);

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSalutationRequest $request, Salutation $Salutation)
    {
        $state = $this->SalutationRepositoryInterface->update($request, $Salutation);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($Salutation)
    {
        $state = $this->SalutationRepositoryInterface->destroy($Salutation);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
