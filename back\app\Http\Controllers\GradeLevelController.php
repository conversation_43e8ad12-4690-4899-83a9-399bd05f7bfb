<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreGradeLevelRequest;
use App\Http\Requests\UpdateGradeLevelRequest;
use App\Models\GradeLevel;
use App\Repositories\Interfaces\GradeLevelRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class GradeLevelController extends Controller
{
    use ApiErrorResponse;

    private $GradeLevelRepositoryInterface;

    public function __construct(GradeLevelRepositoryInterface $GradeLevelRepositoryInterface)
    {
        $this->GradeLevelRepositoryInterface = $GradeLevelRepositoryInterface;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->GradeLevelRepositoryInterface->index();

        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreGradeLevelRequest $request)
    {
        $state = $this->GradeLevelRepositoryInterface->store($request);

        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($GradeLevel)
    {
        $state = $this->GradeLevelRepositoryInterface->show($GradeLevel);

        return response()->json($state, Response::HTTP_OK);
    }

    public function updateGradeSort(Request $requestData)
    {
        $state = $this->GradeLevelRepositoryInterface->updateGradeSort($requestData);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateGradeLevelRequest $request, GradeLevel $GradeLevel)
    {
        $state = $this->GradeLevelRepositoryInterface->update($request, $GradeLevel);

        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($GradeLevel)
    {
        $state = $this->GradeLevelRepositoryInterface->destroy($GradeLevel);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) : '';
        }
    }
}
