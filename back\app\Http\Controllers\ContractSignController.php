<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ContractSign;
use App\Http\Requests\StoreContractSignRequest;
use App\Http\Requests\UpdateContractSignRequest;
use Illuminate\Http\Response;
use App\Repositories\Interfaces\ContractSignRepositoryInterface;
use App\Traits\ApiErrorResponse;
use Illuminate\Support\Facades\Crypt;
use PDF;

class ContractSignController extends Controller
{
    use ApiErrorResponse;

    private $ContractSignRepositoryInterface;


    public function __construct(ContractSignRepositoryInterface $ContractSignRepositoryInterface)
    {
        $this->ContractSignRepositoryInterface = $ContractSignRepositoryInterface;
    }



    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $state = $this->ContractSignRepositoryInterface->index();
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreContractSignRequest $request)
    {
        $state = $this->ContractSignRepositoryInterface->store($request);
        return $state ? $this->successResponse(false, config('constants.DATA_INSERTED')) : '';
    }

    /**
     * Display the specified resource.
     */
    public function show($ContractSign)
    {
        $state = $this->ContractSignRepositoryInterface->show($ContractSign);
        return response()->json($state, Response::HTTP_OK);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateContractSignRequest $request, ContractSign $ContractSign)
    {
        $state = $this->ContractSignRepositoryInterface->update($request, $ContractSign);
        return $state ? $this->successResponse(false, config('constants.DATA_UPDATED')) : '';
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($ContractSign)
    {
        $state = $this->ContractSignRepositoryInterface->destroy($ContractSign);
        if ($state != 1 && $state == 23000) {
            return $this->generalError(config('constants.DELETE_ERROR_MESSAGE'));
        } else {
            return $state ? $this->successResponse(false, config('constants.DATA_DELETED')) :
                $this->generalError(config('constants.GENERAL_ERROR'));;
        }
    }

    public function downloadContractPdf(Request $request)
    {
        // Retrieve the encoded ID from the query parameters
        $encodedId = $request->input('id');
        // Decode the ID using Laravel's decrypt method
        $id = Crypt::decryptString($encodedId);

        $data = ContractSign::where('id', $id)->first();

        $pdfData = [
            'first_signature' => $data->first_signature,
            'second_signature' => $data->second_signature,
        ];
        $pdf = PDF::loadView('contract.Contract', $pdfData);

        return $pdf->stream('Contract.pdf');

        // $data = $request->all();
        // $invoice_file_name = $data['invoice_file_name'];

        // return $fileUrl = asset('storage/app/public/Contracts/' . $invoice_file_name);
    }
}
